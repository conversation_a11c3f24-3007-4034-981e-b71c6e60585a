<!DOCTYPE html>
<html lang="{{ get_locale() }}" dir="{{ 'rtl' if get_locale() == 'ar' else 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ _('Electronic Correspondence System') }}{% endblock %}</title>
    
    <!-- Bootstrap CSS with RTL support -->
    {% if get_locale() == 'ar' %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    {% else %}
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    {% endif %}
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% if get_locale() == 'ar' %}
        <!-- Arabic fonts -->
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    {% endif %}
    
    {% block extra_css %}{% endblock %}
</head>
<body class="{{ 'rtl' if get_locale() == 'ar' else 'ltr' }}">
    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-envelope-open-text me-2"></i>
                {{ config.ORGANIZATION_NAME_AR if get_locale() == 'ar' else config.ORGANIZATION_NAME }}
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>{{ _('Dashboard') }}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope me-1"></i>{{ _('Messages') }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('messages.incoming') }}">
                                <i class="fas fa-inbox me-1"></i>{{ _('Incoming') }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.outgoing') }}">
                                <i class="fas fa-paper-plane me-1"></i>{{ _('Outgoing') }}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.create_message', message_type='incoming') }}">
                                <i class="fas fa-plus me-1"></i>{{ _('New Incoming') }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('messages.create_message', message_type='outgoing') }}">
                                <i class="fas fa-plus me-1"></i>{{ _('New Outgoing') }}
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('messages.search') }}">
                            <i class="fas fa-search me-1"></i>{{ _('Search') }}
                        </a>
                    </li>
                    {% if current_user.role.value in ['admin', 'manager'] %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>{{ _('Administration') }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('admin.panel') }}">
                                <i class="fas fa-chart-bar me-1"></i>{{ _('Admin Panel') }}
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.users') }}">
                                <i class="fas fa-users me-1"></i>{{ _('User Management') }}
                            </a></li>
                            {% if current_user.role.value == 'admin' %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('admin.settings') }}">
                                <i class="fas fa-cog me-1"></i>{{ _('System Settings') }}
                            </a></li>
                            {% endif %}
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-globe me-1"></i>
                            {{ 'العربية' if get_locale() == 'ar' else 'English' }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('set_language', language='en') }}">
                                <i class="flag-icon flag-icon-us me-1"></i>English
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('set_language', language='ar') }}">
                                <i class="flag-icon flag-icon-sa me-1"></i>العربية
                            </a></li>
                        </ul>
                    </li>
                    
                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>
                            {{ current_user.full_name_ar if get_locale() == 'ar' and current_user.full_name_ar else current_user.full_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-user-edit me-1"></i>{{ _('Profile') }}
                            </a></li>
                            <li><a class="dropdown-item" href="#">
                                <i class="fas fa-cog me-1"></i>{{ _('Settings') }}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                <i class="fas fa-sign-out-alt me-1"></i>{{ _('Logout') }}
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}
    
    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- Main Content -->
    <main class="container-fluid mt-4">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <p>&copy; {{ moment().year }} {{ config.ORGANIZATION_NAME_AR if get_locale() == 'ar' else config.ORGANIZATION_NAME }}. {{ _('All rights reserved.') }}</p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
