{% extends "base.html" %}

{% block title %}{{ _('Outgoing Messages') }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-paper-plane me-2"></i>{{ _('Outgoing Messages') }}
            </h2>
            <a href="{{ url_for('messages.create_message', message_type='outgoing') }}" class="btn btn-success">
                <i class="fas fa-plus me-1"></i>{{ _('New Outgoing Message') }}
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="status" class="form-label">{{ _('Status') }}</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">{{ _('All Statuses') }}</option>
                            <option value="pending" {{ 'selected' if request.args.get('status') == 'pending' }}>{{ _('Pending') }}</option>
                            <option value="in_progress" {{ 'selected' if request.args.get('status') == 'in_progress' }}>{{ _('In Progress') }}</option>
                            <option value="completed" {{ 'selected' if request.args.get('status') == 'completed' }}>{{ _('Completed') }}</option>
                            <option value="archived" {{ 'selected' if request.args.get('status') == 'archived' }}>{{ _('Archived') }}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="priority" class="form-label">{{ _('Priority') }}</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">{{ _('All Priorities') }}</option>
                            <option value="low" {{ 'selected' if request.args.get('priority') == 'low' }}>{{ _('Low') }}</option>
                            <option value="medium" {{ 'selected' if request.args.get('priority') == 'medium' }}>{{ _('Medium') }}</option>
                            <option value="high" {{ 'selected' if request.args.get('priority') == 'high' }}>{{ _('High') }}</option>
                            <option value="urgent" {{ 'selected' if request.args.get('priority') == 'urgent' }}>{{ _('Urgent') }}</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">{{ _('Search') }}</label>
                        <input type="text" class="form-control" id="search" name="q" 
                               value="{{ request.args.get('q', '') }}" placeholder="{{ _('Search messages...') }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i>{{ _('Filter') }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Messages Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                {% if messages.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>{{ _('Message Number') }}</th>
                                <th>{{ _('Subject') }}</th>
                                <th>{{ _('Recipient') }}</th>
                                <th>{{ _('Status') }}</th>
                                <th>{{ _('Priority') }}</th>
                                <th>{{ _('Date') }}</th>
                                <th>{{ _('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in messages.items %}
                            <tr>
                                <td>
                                    <span class="badge bg-success">{{ message.message_number }}</span>
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject_ar if get_locale() == 'ar' and message.subject_ar else message.subject }}
                                    </a>
                                    {% if message.is_confidential %}
                                        <i class="fas fa-lock text-warning ms-1" title="{{ _('Confidential') }}"></i>
                                    {% endif %}
                                    {% if message.attachments.count() > 0 %}
                                        <i class="fas fa-paperclip text-info ms-1" title="{{ _('Has Attachments') }}"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if message.recipient %}
                                        {{ message.recipient.full_name_ar if get_locale() == 'ar' and message.recipient.full_name_ar else message.recipient.full_name }}
                                    {% else %}
                                        {{ message.external_recipient }}
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'warning' if message.status.value == 'pending' else 'info' if message.status.value == 'in_progress' else 'success' if message.status.value == 'completed' else 'secondary' }}">
                                        {{ _(message.status.value.replace('_', ' ').title()) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if message.priority.value == 'urgent' else 'warning' if message.priority.value == 'high' else 'info' if message.priority.value == 'medium' else 'secondary' }}">
                                        {{ _(message.priority.value.title()) }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                           class="btn btn-outline-primary" title="{{ _('View') }}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if current_user.role.value in ['admin', 'manager'] or message.sender_id == current_user.id %}
                                        <a href="#" class="btn btn-outline-success" title="{{ _('Print') }}" 
                                           onclick="printMessage({{ message.id }})">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if messages.pages > 1 %}
                <nav aria-label="{{ _('Messages pagination') }}">
                    <ul class="pagination justify-content-center">
                        {% if messages.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('messages.outgoing', page=messages.prev_num, **request.args) }}">
                                    {{ _('Previous') }}
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in messages.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != messages.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('messages.outgoing', page=page_num, **request.args) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if messages.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('messages.outgoing', page=messages.next_num, **request.args) }}">
                                    {{ _('Next') }}
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-paper-plane fa-4x mb-3 opacity-50"></i>
                    <h5>{{ _('No outgoing messages found') }}</h5>
                    <p>{{ _('Start by creating a new outgoing message.') }}</p>
                    <a href="{{ url_for('messages.create_message', message_type='outgoing') }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>{{ _('Create New Message') }}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
