{% extends "base.html" %}

{% block title %}{{ _('User Management') }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-users me-2"></i>{{ _('User Management') }}
            </h2>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus me-1"></i>{{ _('Add User') }}
            </button>
        </div>

        <!-- Users Table -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">{{ _('System Users') }}</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>{{ _('ID') }}</th>
                                <th>{{ _('Full Name') }}</th>
                                <th>{{ _('Username') }}</th>
                                <th>{{ _('Email') }}</th>
                                <th>{{ _('Department') }}</th>
                                <th>{{ _('Role') }}</th>
                                <th>{{ _('Status') }}</th>
                                <th>{{ _('Created Date') }}</th>
                                <th>{{ _('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.id }}</td>
                                <td>
                                    {{ user.full_name_ar if get_locale() == 'ar' and user.full_name_ar else user.full_name }}
                                </td>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>
                                    {{ user.department_ar if get_locale() == 'ar' and user.department_ar else user.department }}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if user.role.value == 'admin' else 'warning' if user.role.value == 'manager' else 'info' }}">
                                        {{ _(user.role.value.title()) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                        {{ _('Active') if user.is_active else _('Inactive') }}
                                    </span>
                                </td>
                                <td>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '-' }}</td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button type="button" class="btn btn-outline-primary" 
                                                onclick="editUser({{ user.id }})" title="{{ _('Edit') }}">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if user.id != current_user.id %}
                                        <button type="button" class="btn btn-outline-{{ 'secondary' if user.is_active else 'success' }}" 
                                                onclick="toggleUserStatus({{ user.id }})" 
                                                title="{{ _('Deactivate') if user.is_active else _('Activate') }}">
                                            <i class="fas fa-{{ 'ban' if user.is_active else 'check' }}"></i>
                                        </button>
                                        {% if current_user.role.value == 'admin' %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deleteUser({{ user.id }})" title="{{ _('Delete') }}">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="9" class="text-center text-muted">{{ _('No users found') }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Pagination -->
            {% if pagination and pagination.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="{{ _('User pagination') }}">
                    <ul class="pagination justify-content-center mb-0">
                        {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.users', page=pagination.prev_num) }}">
                                <i class="fas fa-chevron-left"></i> {{ _('Previous') }}
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in pagination.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('admin.users', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% endif %}
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.users', page=pagination.next_num) }}">
                                {{ _('Next') }} <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addUserModalLabel">
                    <i class="fas fa-user-plus me-2"></i>{{ _('Add New User') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{{ _('Close') }}"></button>
            </div>
            <form id="addUserForm" method="POST" action="{{ url_for('admin.create_user') }}">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="username" class="form-label">{{ _('Username') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">{{ _('Email') }} <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name" class="form-label">{{ _('Full Name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="full_name_ar" class="form-label">{{ _('Full Name (Arabic)') }}</label>
                                <input type="text" class="form-control" id="full_name_ar" name="full_name_ar" dir="rtl">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department" class="form-label">{{ _('Department') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="department" name="department" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="department_ar" class="form-label">{{ _('Department (Arabic)') }}</label>
                                <input type="text" class="form-control" id="department_ar" name="department_ar" dir="rtl">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="role" class="form-label">{{ _('Role') }} <span class="text-danger">*</span></label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">{{ _('Select role') }}</option>
                                    <option value="user">{{ _('User') }}</option>
                                    <option value="manager">{{ _('Manager') }}</option>
                                    {% if current_user.role.value == 'admin' %}
                                    <option value="admin">{{ _('Admin') }}</option>
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="password" class="form-label">{{ _('Password') }} <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                {{ _('Active User') }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ _('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>{{ _('Create User') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editUserModalLabel">
                    <i class="fas fa-user-edit me-2"></i>{{ _('Edit User') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="{{ _('Close') }}"></button>
            </div>
            <form id="editUserForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" id="edit_user_id" name="user_id">
                    <!-- Same form fields as add user modal -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_username" class="form-label">{{ _('Username') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_username" name="username" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_email" class="form-label">{{ _('Email') }} <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" id="edit_email" name="email" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_full_name" class="form-label">{{ _('Full Name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_full_name_ar" class="form-label">{{ _('Full Name (Arabic)') }}</label>
                                <input type="text" class="form-control" id="edit_full_name_ar" name="full_name_ar" dir="rtl">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_department" class="form-label">{{ _('Department') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="edit_department" name="department" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_department_ar" class="form-label">{{ _('Department (Arabic)') }}</label>
                                <input type="text" class="form-control" id="edit_department_ar" name="department_ar" dir="rtl">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_role" class="form-label">{{ _('Role') }} <span class="text-danger">*</span></label>
                                <select class="form-select" id="edit_role" name="role" required>
                                    <option value="user">{{ _('User') }}</option>
                                    <option value="manager">{{ _('Manager') }}</option>
                                    {% if current_user.role.value == 'admin' %}
                                    <option value="admin">{{ _('Admin') }}</option>
                                    {% endif %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_password" class="form-label">{{ _('New Password') }}</label>
                                <input type="password" class="form-control" id="edit_password" name="password">
                                <small class="form-text text-muted">{{ _('Leave empty to keep current password') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ _('Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>{{ _('Update User') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function editUser(userId) {
    // Fetch user data and populate edit modal
    fetch(`/admin/users/${userId}`)
        .then(response => response.json())
        .then(user => {
            document.getElementById('edit_user_id').value = user.id;
            document.getElementById('edit_username').value = user.username;
            document.getElementById('edit_email').value = user.email;
            document.getElementById('edit_full_name').value = user.full_name;
            document.getElementById('edit_full_name_ar').value = user.full_name_ar || '';
            document.getElementById('edit_department').value = user.department;
            document.getElementById('edit_department_ar').value = user.department_ar || '';
            document.getElementById('edit_role').value = user.role;
            
            document.getElementById('editUserForm').action = `/admin/users/${userId}/edit`;
            
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ _("Error loading user data") }}');
        });
}

function toggleUserStatus(userId) {
    if (confirm('{{ _("Are you sure you want to change this user\'s status?") }}')) {
        fetch(`/admin/users/${userId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ _("Error updating user status") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ _("Error updating user status") }}');
        });
    }
}

function deleteUser(userId) {
    if (confirm('{{ _("Are you sure you want to delete this user? This action cannot be undone.") }}')) {
        fetch(`/admin/users/${userId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ _("Error deleting user") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ _("Error deleting user") }}');
        });
    }
}
</script>
{% endblock %}
