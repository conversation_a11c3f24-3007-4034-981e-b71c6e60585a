#!/usr/bin/env python3
"""
Electronic Correspondence System - Installation Script
This script helps set up the system for first-time use.
"""

import os
import sys
import subprocess
from pathlib import Path

def print_header():
    print("=" * 60)
    print("Electronic Correspondence System - Installation")
    print("نظام المراسلات الإلكترونية - التثبيت")
    print("=" * 60)
    print()

def check_python_version():
    """Check if Python version is compatible"""
    print("Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required.")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} is compatible.")
    return True

def install_dependencies():
    """Install required Python packages"""
    print("\nInstalling dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully.")
        return True
    except subprocess.CalledProcessError:
        print("❌ Error installing dependencies.")
        print("   Please run: pip install -r requirements.txt")
        return False

def create_directories():
    """Create necessary directories"""
    print("\nCreating directories...")
    directories = [
        "uploads",
        "static/css",
        "static/js", 
        "static/images",
        "templates/auth",
        "templates/messages",
        "templates/admin",
        "templates/components",
        "translations/en/LC_MESSAGES",
        "translations/ar/LC_MESSAGES"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories created successfully.")

def create_env_file():
    """Create .env file with default settings"""
    print("\nCreating environment configuration...")
    
    env_content = """# Electronic Correspondence System Configuration
FLASK_CONFIG=development
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=True
SECRET_KEY=change-this-secret-key-in-production
DATABASE_URL=sqlite:///correspondence_system.db

# Optional: Email configuration
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=True
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password
"""
    
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ Environment file (.env) created.")
    else:
        print("ℹ️  Environment file already exists.")

def initialize_database():
    """Initialize the database"""
    print("\nInitializing database...")
    try:
        from app import create_app
        app = create_app()
        with app.app_context():
            from models import db
            db.create_all()
            print("✅ Database initialized successfully.")
        return True
    except Exception as e:
        print(f"❌ Error initializing database: {e}")
        return False

def create_admin_user():
    """Create default admin user"""
    print("\nCreating default admin user...")
    try:
        from app import create_app
        from models import db, User, UserRole
        
        app = create_app()
        with app.app_context():
            # Check if admin already exists
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("ℹ️  Admin user already exists.")
                return True
            
            # Create admin user
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                full_name_ar='مدير النظام',
                role=UserRole.ADMIN,
                department='IT',
                department_ar='تقنية المعلومات',
                is_active=True,
                preferred_language='en'
            )
            admin.set_password('admin123')
            
            db.session.add(admin)
            db.session.commit()
            print("✅ Default admin user created.")
            print("   Username: admin")
            print("   Password: admin123")
            print("   ⚠️  Please change the password after first login!")
        return True
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return False

def print_completion_message():
    """Print installation completion message"""
    print("\n" + "=" * 60)
    print("🎉 Installation completed successfully!")
    print("=" * 60)
    print()
    print("Next steps:")
    print("1. Run the application:")
    print("   python run.py")
    print()
    print("2. Open your web browser and go to:")
    print("   http://localhost:5000")
    print()
    print("3. Login with default credentials:")
    print("   Username: admin")
    print("   Password: admin123")
    print()
    print("4. Important: Change the admin password after first login!")
    print()
    print("For more information, see README.md")
    print()

def main():
    """Main installation function"""
    print_header()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed at dependency installation.")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Create environment file
    create_env_file()
    
    # Initialize database
    if not initialize_database():
        print("\n❌ Installation failed at database initialization.")
        sys.exit(1)
    
    # Create admin user
    if not create_admin_user():
        print("\n❌ Installation failed at admin user creation.")
        sys.exit(1)
    
    # Print completion message
    print_completion_message()

if __name__ == "__main__":
    main()
