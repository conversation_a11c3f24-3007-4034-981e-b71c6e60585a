{% extends "base.html" %}

{% block title %}{{ _('Dashboard') }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-tachometer-alt me-2"></i>{{ _('Dashboard') }}
            </h2>
            <div class="text-muted">
                {{ _('Welcome back') }}, {{ current_user.full_name_ar if get_locale() == 'ar' and current_user.full_name_ar else current_user.full_name }}
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_messages }}</h4>
                        <p class="card-text">{{ _('Total Messages') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-envelope fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ pending_messages }}</h4>
                        <p class="card-text">{{ _('Pending Messages') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ my_messages }}</h4>
                        <p class="card-text">{{ _('My Messages') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ pending_workflows|length }}</h4>
                        <p class="card-text">{{ _('Pending Actions') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>{{ _('Quick Actions') }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('messages.create_message', message_type='incoming') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-plus me-1"></i>{{ _('New Incoming') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('messages.create_message', message_type='outgoing') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-plus me-1"></i>{{ _('New Outgoing') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('messages.search') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-search me-1"></i>{{ _('Search Messages') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('messages.incoming') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-inbox me-1"></i>{{ _('View All') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Messages and Pending Workflows -->
<div class="row">
    <!-- Recent Messages -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>{{ _('Recent Messages') }}
                </h5>
                <a href="{{ url_for('messages.incoming') }}" class="btn btn-sm btn-outline-primary">
                    {{ _('View All') }}
                </a>
            </div>
            <div class="card-body">
                {% if recent_messages %}
                    <div class="list-group list-group-flush">
                        {% for message in recent_messages %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                            {{ message.subject_ar if get_locale() == 'ar' and message.subject_ar else message.subject }}
                                        </a>
                                    </h6>
                                    <p class="mb-1 text-muted small">
                                        {{ _('From') }}: {{ message.sender.full_name_ar if get_locale() == 'ar' and message.sender.full_name_ar else message.sender.full_name }}
                                    </p>
                                    <small class="text-muted">{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                                <span class="badge bg-{{ 'primary' if message.message_type.value == 'incoming' else 'success' if message.message_type.value == 'outgoing' else 'info' }}">
                                    {{ _(message.message_type.value.title()) }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-inbox fa-3x mb-3 opacity-50"></i>
                        <p>{{ _('No recent messages') }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Pending Workflows -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks me-2"></i>{{ _('Pending Actions') }}
                </h5>
            </div>
            <div class="card-body">
                {% if pending_workflows %}
                    <div class="list-group list-group-flush">
                        {% for workflow in pending_workflows %}
                        <div class="list-group-item border-0 px-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">
                                        <a href="{{ url_for('messages.view_message', id=workflow.message.id) }}" class="text-decoration-none">
                                            {{ workflow.message.subject_ar if get_locale() == 'ar' and workflow.message.subject_ar else workflow.message.subject }}
                                        </a>
                                    </h6>
                                    <p class="mb-1 text-muted small">
                                        {{ _('Action: %(action)s', action=_(workflow.action.title())) }}
                                    </p>
                                    <small class="text-muted">{{ workflow.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                                <span class="badge bg-warning">
                                    {{ _('Pending') }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-check-circle fa-3x mb-3 opacity-50"></i>
                        <p>{{ _('No pending actions') }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
