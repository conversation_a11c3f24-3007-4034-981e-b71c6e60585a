// Electronic Correspondence System - JavaScript Functions

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);

    // Confirm delete actions
    document.querySelectorAll('[data-confirm-delete]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            var message = this.getAttribute('data-confirm-delete') || 'Are you sure you want to delete this item?';
            if (confirm(message)) {
                window.location.href = this.href;
            }
        });
    });

    // Form validation
    var forms = document.querySelectorAll('.needs-validation');
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // File upload preview
    document.querySelectorAll('input[type="file"]').forEach(function(input) {
        input.addEventListener('change', function(e) {
            var files = e.target.files;
            var preview = document.getElementById(this.id + '_preview');
            if (preview) {
                preview.innerHTML = '';
                for (var i = 0; i < files.length; i++) {
                    var file = files[i];
                    var fileInfo = document.createElement('div');
                    fileInfo.className = 'alert alert-info alert-sm';
                    fileInfo.innerHTML = '<i class="fas fa-file me-2"></i>' + file.name + ' (' + formatFileSize(file.size) + ')';
                    preview.appendChild(fileInfo);
                }
            }
        });
    });

    // Search functionality
    var searchInput = document.getElementById('search-input');
    if (searchInput) {
        var searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(function() {
                performSearch(searchInput.value);
            }, 500);
        });
    }

    // Auto-save draft functionality
    var draftForms = document.querySelectorAll('.auto-save-draft');
    draftForms.forEach(function(form) {
        var inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(function(input) {
            input.addEventListener('input', function() {
                saveDraft(form);
            });
        });
    });

    // Message status updates
    document.querySelectorAll('.status-update').forEach(function(element) {
        element.addEventListener('change', function() {
            updateMessageStatus(this.dataset.messageId, this.value);
        });
    });

    // Priority color coding
    updatePriorityColors();

    // Initialize date pickers
    initializeDatePickers();

    // Setup AJAX for dynamic content
    setupAjaxHandlers();
});

// Utility Functions
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    var k = 1024;
    var sizes = ['Bytes', 'KB', 'MB', 'GB'];
    var i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showLoading(element) {
    element.innerHTML = '<span class="loading"></span> Loading...';
    element.disabled = true;
}

function hideLoading(element, originalText) {
    element.innerHTML = originalText;
    element.disabled = false;
}

function showNotification(message, type = 'info') {
    var alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(alertDiv);
    
    setTimeout(function() {
        alertDiv.remove();
    }, 5000);
}

// Search functionality
function performSearch(query) {
    if (query.length < 2) return;
    
    fetch(`/messages/search?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        updateSearchResults(data);
    })
    .catch(error => {
        console.error('Search error:', error);
    });
}

function updateSearchResults(data) {
    var resultsContainer = document.getElementById('search-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = data.html;
    }
}

// Draft saving
function saveDraft(form) {
    var formData = new FormData(form);
    formData.append('save_draft', '1');
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Draft saved automatically', 'success');
        }
    })
    .catch(error => {
        console.error('Draft save error:', error);
    });
}

// Message status updates
function updateMessageStatus(messageId, status) {
    fetch(`/messages/${messageId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Status updated successfully', 'success');
            location.reload();
        } else {
            showNotification('Error updating status', 'error');
        }
    })
    .catch(error => {
        console.error('Status update error:', error);
        showNotification('Error updating status', 'error');
    });
}

// Priority color coding
function updatePriorityColors() {
    document.querySelectorAll('.priority-indicator').forEach(function(element) {
        var priority = element.dataset.priority;
        switch(priority) {
            case 'urgent':
                element.className += ' bg-danger';
                break;
            case 'high':
                element.className += ' bg-warning';
                break;
            case 'medium':
                element.className += ' bg-info';
                break;
            case 'low':
                element.className += ' bg-secondary';
                break;
        }
    });
}

// Date picker initialization
function initializeDatePickers() {
    document.querySelectorAll('input[type="date"]').forEach(function(input) {
        // Add calendar icon
        var wrapper = document.createElement('div');
        wrapper.className = 'input-group';
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(input);
        
        var icon = document.createElement('span');
        icon.className = 'input-group-text';
        icon.innerHTML = '<i class="fas fa-calendar-alt"></i>';
        wrapper.appendChild(icon);
    });
}

// AJAX handlers
function setupAjaxHandlers() {
    // Handle AJAX form submissions
    document.querySelectorAll('.ajax-form').forEach(function(form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            var submitBtn = form.querySelector('button[type="submit"]');
            var originalText = submitBtn.innerHTML;
            showLoading(submitBtn);
            
            var formData = new FormData(form);
            
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                hideLoading(submitBtn, originalText);
                
                if (data.success) {
                    showNotification(data.message, 'success');
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    }
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                hideLoading(submitBtn, originalText);
                showNotification('An error occurred', 'error');
                console.error('Form submission error:', error);
            });
        });
    });
}

// Print functionality
function printMessage(messageId) {
    window.open(`/messages/${messageId}/print`, '_blank');
}

// Export functionality
function exportMessages(format) {
    var params = new URLSearchParams(window.location.search);
    params.set('export', format);
    window.location.href = '/messages/export?' + params.toString();
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+N for new message
    if (e.ctrlKey && e.key === 'n') {
        e.preventDefault();
        window.location.href = '/messages/create/incoming';
    }
    
    // Ctrl+F for search
    if (e.ctrlKey && e.key === 'f') {
        e.preventDefault();
        var searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape to close modals
    if (e.key === 'Escape') {
        var modals = document.querySelectorAll('.modal.show');
        modals.forEach(function(modal) {
            bootstrap.Modal.getInstance(modal).hide();
        });
    }
});

// Theme switching (if implemented)
function switchTheme(theme) {
    document.body.className = document.body.className.replace(/theme-\w+/, '');
    document.body.classList.add('theme-' + theme);
    localStorage.setItem('theme', theme);
}

// Load saved theme
var savedTheme = localStorage.getItem('theme');
if (savedTheme) {
    switchTheme(savedTheme);
}
