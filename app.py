from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, session, Blueprint, send_file, make_response
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_babel import Babel, gettext, ngettext, lazy_gettext, get_locale
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import os
import uuid
from config import config
from models import db, User, Message, Attachment, Workflow, SystemSettings, Organization, LetterTemplate, DigitalSignature, OfficialStamp, UserRole, MessageType, MessageStatus, Priority

# Import docx libraries at the top level
try:
    from docx import Document
    from docx.shared import Inches
    from docx.enum.text import WD_ALIGN_PARAGRAPH
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

# Moment.js-like functionality for templates
class MomentJS:
    def __init__(self, timestamp=None):
        if timestamp is None:
            self.timestamp = datetime.now()
        elif isinstance(timestamp, datetime):
            self.timestamp = timestamp
        else:
            self.timestamp = datetime.fromtimestamp(timestamp)

    def format(self, fmt):
        """Format datetime using Python strftime format"""
        format_map = {
            'DD': '%d',
            'MM': '%m',
            'YYYY': '%Y',
            'MMMM': '%B',
            'MMM': '%b',
            'HH': '%H',
            'mm': '%M',
            'ss': '%S'
        }

        # Replace moment.js format with Python format
        python_fmt = fmt
        for moment_fmt, python_equiv in format_map.items():
            python_fmt = python_fmt.replace(moment_fmt, python_equiv)

        return self.timestamp.strftime(python_fmt)

    @property
    def year(self):
        return self.timestamp.year

def moment(timestamp=None):
    """Create a moment object for template use"""
    return MomentJS(timestamp)

# Language detection function
def get_locale():
    # Check if user manually selected a language
    if 'language' in session:
        return session['language']

    # Check user's preferred language from browser
    return request.accept_languages.best_match(['ar', 'en']) or 'en'

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)

    # Initialize Babel
    babel = Babel(app)
    babel.init_app(app, locale_selector=get_locale)

    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = lazy_gettext('Please log in to access this page.')
    login_manager.login_message_category = 'info'
    
    # Add template globals for translation functions
    app.jinja_env.globals.update(
        gettext=gettext,
        _=gettext,
        get_locale=get_locale,
        config=app.config,
        moment=moment
    )

    # Add template filters
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """Convert newlines to <br> tags"""
        if text:
            return text.replace('\n', '<br>')
        return text
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Create upload directory
    upload_dir = os.path.join(app.root_path, app.config['UPLOAD_FOLDER'])
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
    
    # Helper functions
    def allowed_file(filename):
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

    def generate_letter_preview(message_data):
        """Generate HTML preview of the letter"""
        try:
            # Get organization settings
            org_name = app.config.get('ORGANIZATION_NAME', 'Electronic Correspondence System')
            org_name_ar = app.config.get('ORGANIZATION_NAME_AR', 'نظام المراسلات الإلكترونية')

            # Determine language
            locale = get_locale()
            is_arabic = locale == 'ar'

            # Build recipient info
            recipient_info = ""
            if 'recipient' in message_data and message_data['recipient']:
                user = message_data['recipient']
                recipient_info = user.full_name_ar if is_arabic and user.full_name_ar else user.full_name
                if user.department:
                    dept = user.department_ar if is_arabic and user.department_ar else user.department
                    recipient_info += f"<br>{dept}"
            elif 'organization' in message_data and message_data['organization']:
                org = message_data['organization']
                recipient_info = org.name_ar if is_arabic and org.name_ar else org.name
                if message_data.get('external_recipient'):
                    recipient_info += f"<br>{message_data['external_recipient']}"
            elif message_data.get('external_recipient'):
                recipient_info = message_data['external_recipient']

            # Get content
            subject = message_data.get('subject_ar') if is_arabic and message_data.get('subject_ar') else message_data.get('subject', '')
            content = message_data.get('content_ar') if is_arabic and message_data.get('content_ar') else message_data.get('content', '')

            # Generate HTML
            html = f"""
            <!DOCTYPE html>
            <html dir="{'rtl' if is_arabic else 'ltr'}" lang="{'ar' if is_arabic else 'en'}">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Letter Preview</title>
                <style>
                    body {{
                        font-family: {'Tahoma, Arial' if is_arabic else 'Arial, sans-serif'};
                        line-height: 1.6;
                        margin: 20px;
                        direction: {'rtl' if is_arabic else 'ltr'};
                    }}
                    .header {{
                        text-align: center;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }}
                    .org-name {{
                        font-size: 24px;
                        font-weight: bold;
                        color: #333;
                    }}
                    .letter-info {{
                        margin-bottom: 30px;
                    }}
                    .recipient {{
                        margin-bottom: 20px;
                        padding: 10px;
                        background-color: #f8f9fa;
                        border-left: 4px solid #007bff;
                    }}
                    .subject {{
                        font-size: 18px;
                        font-weight: bold;
                        margin-bottom: 20px;
                        text-align: center;
                        text-decoration: underline;
                    }}
                    .content {{
                        margin-bottom: 30px;
                        text-align: justify;
                    }}
                    .signature {{
                        margin-top: 50px;
                        text-align: {'right' if is_arabic else 'left'};
                    }}
                    .date {{
                        text-align: {'left' if is_arabic else 'right'};
                        margin-bottom: 20px;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="org-name">{org_name_ar if is_arabic else org_name}</div>
                </div>

                <div class="date">
                    {'التاريخ' if is_arabic else 'Date'}: {message_data['created_at'].strftime('%Y-%m-%d')}
                </div>

                {f'<div class="letter-info"><strong>{"رقم المرجع" if is_arabic else "Reference"}: </strong>{message_data.get("reference_number", "")}</div>' if message_data.get('reference_number') else ''}

                <div class="recipient">
                    <strong>{'إلى' if is_arabic else 'To'}:</strong><br>
                    {recipient_info}
                </div>

                <div class="subject">
                    {'الموضوع' if is_arabic else 'Subject'}: {subject}
                </div>

                <div class="content">
                    {content.replace(chr(10), '<br>')}
                </div>

                <div class="signature">
                    <strong>{message_data['sender'].full_name_ar if is_arabic and message_data['sender'].full_name_ar else message_data['sender'].full_name}</strong><br>
                    {message_data['sender'].department_ar if is_arabic and message_data['sender'].department_ar else message_data['sender'].department or ''}
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            return f"<html><body><h3>Error generating preview</h3><p>{str(e)}</p></body></html>"

    def generate_letter_document(message):
        """Generate Word document for the message"""
        try:
            if not DOCX_AVAILABLE:
                raise Exception("python-docx library is required for document generation. Please install it with: pip install python-docx")

            # Create new document
            doc = Document()

            # Set up document properties
            doc.core_properties.title = f"Letter {message.message_number}"
            doc.core_properties.author = message.sender.full_name

            # Add header
            header = doc.sections[0].header
            header_para = header.paragraphs[0]
            header_para.text = app.config.get('ORGANIZATION_NAME', 'Electronic Correspondence System')
            header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Add date
            date_para = doc.add_paragraph()
            date_para.text = f"Date: {message.created_at.strftime('%Y-%m-%d')}"
            date_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

            # Add reference number if exists
            if message.reference_number:
                ref_para = doc.add_paragraph()
                ref_para.text = f"Reference: {message.reference_number}"
                ref_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

            # Add recipient
            doc.add_paragraph()  # Empty line
            recipient_para = doc.add_paragraph()
            recipient_para.text = "To:"
            recipient_para.add_run("\n")

            if message.recipient:
                recipient_para.add_run(message.recipient.full_name)
                if message.recipient.department:
                    recipient_para.add_run(f"\n{message.recipient.department}")
            elif message.organization:
                recipient_para.add_run(message.organization.name)
                if message.external_recipient:
                    recipient_para.add_run(f"\nAttn: {message.external_recipient}")
            elif message.external_recipient:
                recipient_para.add_run(message.external_recipient)

            # Add subject
            doc.add_paragraph()  # Empty line
            subject_para = doc.add_paragraph()
            subject_para.text = f"Subject: {message.subject}"
            subject_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # Add content
            doc.add_paragraph()  # Empty line
            content_para = doc.add_paragraph()
            content_para.text = message.content
            content_para.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY

            # Add signature
            doc.add_paragraph()  # Empty line
            doc.add_paragraph()  # Empty line
            signature_para = doc.add_paragraph()
            signature_para.text = f"Sincerely,\n\n{message.sender.full_name}"
            if message.sender.department:
                signature_para.add_run(f"\n{message.sender.department}")

            # Save document
            documents_dir = os.path.join(app.root_path, 'generated_documents')
            if not os.path.exists(documents_dir):
                os.makedirs(documents_dir)

            filename = f"letter_{message.message_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
            file_path = os.path.join(documents_dir, filename)
            doc.save(file_path)

            return file_path

        except Exception as e:
            raise Exception(f"Error generating document: {str(e)}")

    def generate_print_version(message):
        """Generate print-friendly HTML version"""
        try:
            # Similar to preview but optimized for printing
            locale = get_locale()
            is_arabic = locale == 'ar'

            org_name = app.config.get('ORGANIZATION_NAME', 'Electronic Correspondence System')
            org_name_ar = app.config.get('ORGANIZATION_NAME_AR', 'نظام المراسلات الإلكترونية')

            # Build recipient info
            recipient_info = ""
            if message.recipient:
                recipient_info = message.recipient.full_name_ar if is_arabic and message.recipient.full_name_ar else message.recipient.full_name
                if message.recipient.department:
                    dept = message.recipient.department_ar if is_arabic and message.recipient.department_ar else message.recipient.department
                    recipient_info += f"<br>{dept}"
            elif message.organization:
                recipient_info = message.organization.name_ar if is_arabic and message.organization.name_ar else message.organization.name
                if message.external_recipient:
                    recipient_info += f"<br>{message.external_recipient}"
            elif message.external_recipient:
                recipient_info = message.external_recipient

            subject = message.subject_ar if is_arabic and message.subject_ar else message.subject
            content = message.content_ar if is_arabic and message.content_ar else message.content

            html = f"""
            <!DOCTYPE html>
            <html dir="{'rtl' if is_arabic else 'ltr'}" lang="{'ar' if is_arabic else 'en'}">
            <head>
                <meta charset="UTF-8">
                <title>Print - {message.message_number}</title>
                <style>
                    @media print {{
                        body {{ margin: 0; }}
                        .no-print {{ display: none; }}
                    }}
                    body {{
                        font-family: {'Tahoma, Arial' if is_arabic else 'Arial, sans-serif'};
                        line-height: 1.6;
                        margin: 20px;
                        direction: {'rtl' if is_arabic else 'ltr'};
                    }}
                    .header {{
                        text-align: center;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }}
                    .print-button {{
                        position: fixed;
                        top: 10px;
                        right: 10px;
                        z-index: 1000;
                    }}
                </style>
                <script>
                    window.onload = function() {{
                        window.print();
                    }}
                </script>
            </head>
            <body>
                <button class="print-button no-print btn btn-primary" onclick="window.print()">Print</button>

                <div class="header">
                    <div style="font-size: 24px; font-weight: bold;">{org_name_ar if is_arabic else org_name}</div>
                </div>

                <div style="text-align: {'left' if is_arabic else 'right'}; margin-bottom: 20px;">
                    {'التاريخ' if is_arabic else 'Date'}: {message.created_at.strftime('%Y-%m-%d')}
                </div>

                {f'<div style="margin-bottom: 20px;"><strong>{"رقم المرجع" if is_arabic else "Reference"}: </strong>{message.reference_number}</div>' if message.reference_number else ''}

                <div style="margin-bottom: 20px; padding: 10px; background-color: #f8f9fa; border-left: 4px solid #007bff;">
                    <strong>{'إلى' if is_arabic else 'To'}:</strong><br>
                    {recipient_info}
                </div>

                <div style="font-size: 18px; font-weight: bold; margin-bottom: 20px; text-align: center; text-decoration: underline;">
                    {'الموضوع' if is_arabic else 'Subject'}: {subject}
                </div>

                <div style="margin-bottom: 30px; text-align: justify;">
                    {content.replace(chr(10), '<br>')}
                </div>

                <div style="margin-top: 50px; text-align: {'right' if is_arabic else 'left'};">
                    <strong>{message.sender.full_name_ar if is_arabic and message.sender.full_name_ar else message.sender.full_name}</strong><br>
                    {message.sender.department_ar if is_arabic and message.sender.department_ar else message.sender.department or ''}
                </div>
            </body>
            </html>
            """

            return html

        except Exception as e:
            return f"<html><body><h3>Error generating print version</h3><p>{str(e)}</p></body></html>"
    
    def generate_message_number(message_type):
        """Generate unique message number based on type and date"""
        today = datetime.now()
        year = today.year
        month = today.month
        
        # Count messages of this type for this month
        count = Message.query.filter(
            Message.message_type == message_type,
            db.extract('year', Message.created_at) == year,
            db.extract('month', Message.created_at) == month
        ).count() + 1
        
        type_prefix = {
            MessageType.INCOMING: 'IN',
            MessageType.OUTGOING: 'OUT',
            MessageType.INTERNAL: 'INT'
        }
        
        return f"{type_prefix[message_type]}-{year}-{month:02d}-{count:04d}"
    
    # Language route
    @app.route('/set_language/<language>')
    def set_language(language=None):
        if language in app.config['LANGUAGES']:
            session['language'] = language
        return redirect(request.referrer or url_for('index'))

    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))
    

    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        # Get statistics for dashboard
        total_messages = Message.query.count()
        pending_messages = Message.query.filter_by(status=MessageStatus.PENDING).count()
        my_messages = Message.query.filter_by(recipient_id=current_user.id).count()
        
        # Recent messages
        recent_messages = Message.query.filter_by(recipient_id=current_user.id)\
                                     .order_by(Message.created_at.desc())\
                                     .limit(5).all()
        
        # Pending workflows
        pending_workflows = Workflow.query.filter_by(to_user_id=current_user.id, is_read=False)\
                                         .order_by(Workflow.created_at.desc())\
                                         .limit(5).all()
        
        return render_template('dashboard.html',
                             total_messages=total_messages,
                             pending_messages=pending_messages,
                             my_messages=my_messages,
                             recent_messages=recent_messages,
                             pending_workflows=pending_workflows)
    
    # Authentication Blueprint
    auth = Blueprint('auth', __name__)
    
    @auth.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form['username']
            password = request.form['password']
            remember = bool(request.form.get('remember'))
            
            user = User.query.filter_by(username=username).first()
            
            if user and user.check_password(password) and user.is_active:
                login_user(user, remember=remember)
                user.last_login = datetime.utcnow()
                db.session.commit()
                
                next_page = request.args.get('next')
                if not next_page or not next_page.startswith('/'):
                    next_page = url_for('dashboard')
                return redirect(next_page)
            else:
                flash(gettext('Invalid username or password'), 'error')
        
        return render_template('auth/login.html')
    
    @auth.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash(gettext('You have been logged out successfully'), 'success')
        return redirect(url_for('auth.login'))
    
    app.register_blueprint(auth, url_prefix='/auth')
    
    # Messages Blueprint
    messages_bp = Blueprint('messages', __name__)
    
    @messages_bp.route('/incoming')
    @login_required
    def incoming():
        page = request.args.get('page', 1, type=int)
        messages = Message.query.filter_by(message_type=MessageType.INCOMING)\
                               .order_by(Message.created_at.desc())\
                               .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])
        return render_template('messages/incoming.html', messages=messages)
    
    @messages_bp.route('/outgoing')
    @login_required
    def outgoing():
        page = request.args.get('page', 1, type=int)
        messages = Message.query.filter_by(message_type=MessageType.OUTGOING)\
                               .order_by(Message.created_at.desc())\
                               .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])
        return render_template('messages/outgoing.html', messages=messages)
    
    @messages_bp.route('/create/<message_type>', methods=['GET', 'POST'])
    @login_required
    def create_message(message_type):
        if request.method == 'POST':
            # Create new message
            message = Message(
                message_number=generate_message_number(MessageType(message_type)),
                subject=request.form['subject'],
                subject_ar=request.form.get('subject_ar', ''),
                content=request.form['content'],
                content_ar=request.form.get('content_ar', ''),
                message_type=MessageType(message_type),
                sender_id=current_user.id,
                priority=Priority(request.form.get('priority', 'medium')),
                external_sender=request.form.get('external_sender', ''),
                external_recipient=request.form.get('external_recipient', ''),
                reference_number=request.form.get('reference_number', ''),
                notes=request.form.get('notes', ''),
                is_confidential=bool(request.form.get('is_confidential'))
            )
            
            # Set recipient if internal message
            if request.form.get('recipient_id'):
                message.recipient_id = int(request.form['recipient_id'])
            
            # Set dates
            if request.form.get('received_date'):
                message.received_date = datetime.strptime(request.form['received_date'], '%Y-%m-%d')
            if request.form.get('due_date'):
                message.due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d')
            
            db.session.add(message)
            db.session.commit()
            
            # Handle file uploads
            if 'attachments' in request.files:
                files = request.files.getlist('attachments')
                for file in files:
                    if file and file.filename and allowed_file(file.filename):
                        filename = secure_filename(file.filename)
                        unique_filename = f"{uuid.uuid4()}_{filename}"
                        file_path = os.path.join(upload_dir, unique_filename)
                        file.save(file_path)
                        
                        attachment = Attachment(
                            filename=unique_filename,
                            original_filename=filename,
                            file_path=file_path,
                            file_size=os.path.getsize(file_path),
                            file_type=filename.rsplit('.', 1)[1].lower(),
                            uploaded_by=current_user.id,
                            message_id=message.id
                        )
                        db.session.add(attachment)
            
            db.session.commit()
            flash(gettext('Message created successfully'), 'success')
            return redirect(url_for('messages.view_message', id=message.id))
        
        # Get users for recipient selection
        users = User.query.filter_by(is_active=True).all()
        return render_template('messages/create.html', message_type=message_type, users=users)

    @messages_bp.route('/create-outgoing', methods=['GET', 'POST'])
    @login_required
    def create_outgoing():
        if request.method == 'POST':
            action = request.form.get('action', 'create')

            try:
                # Create new outgoing message
                message = Message(
                    message_number=generate_message_number(MessageType.OUTGOING),
                    subject=request.form['subject'],
                    subject_ar=request.form.get('subject_ar', ''),
                    content=request.form['content'],
                    content_ar=request.form.get('content_ar', ''),
                    message_type=MessageType.OUTGOING,
                    sender_id=current_user.id,
                    priority=Priority(request.form.get('priority', 'medium')),
                    reference_number=request.form.get('reference_number', ''),
                    notes=request.form.get('notes', ''),
                    is_confidential=bool(request.form.get('is_confidential')),
                    letter_type=request.form.get('letter_type'),
                    letter_template_id=request.form.get('letter_template_id') or None,
                    authorized_by=request.form.get('authorized_by') or None,
                    signature_id=request.form.get('signature_id') or None,
                    stamp_id=request.form.get('stamp_id') or None
                )

                # Handle recipient based on type
                recipient_type = request.form.get('recipient_type', 'internal')
                if recipient_type == 'internal':
                    message.recipient_id = request.form.get('recipient_id')
                elif recipient_type == 'organization':
                    message.organization_id = request.form.get('organization_id')
                    message.external_recipient = request.form.get('org_contact_person', '')
                elif recipient_type == 'external':
                    message.external_recipient = request.form.get('external_recipient')

                # Set status based on action
                if action == 'draft':
                    message.status = MessageStatus.PENDING
                else:
                    message.status = MessageStatus.IN_PROGRESS

                db.session.add(message)
                db.session.commit()

                # Handle file uploads
                if 'attachments' in request.files:
                    files = request.files.getlist('attachments')
                    for file in files:
                        if file and file.filename and allowed_file(file.filename):
                            filename = secure_filename(file.filename)
                            unique_filename = f"{uuid.uuid4()}_{filename}"
                            file_path = os.path.join(upload_dir, unique_filename)
                            file.save(file_path)

                            attachment = Attachment(
                                filename=unique_filename,
                                original_filename=filename,
                                file_path=file_path,
                                file_size=os.path.getsize(file_path),
                                file_type=filename.rsplit('.', 1)[1].lower(),
                                uploaded_by=current_user.id,
                                message_id=message.id
                            )
                            db.session.add(attachment)

                db.session.commit()

                # Generate document if requested
                if action == 'create_and_generate':
                    try:
                        document_path = generate_letter_document(message)
                        message.generated_document_path = document_path
                        db.session.commit()
                    except Exception as e:
                        flash(gettext('Message created but document generation failed: %(error)s', error=str(e)), 'warning')

                if action == 'draft':
                    if request.is_json:
                        return jsonify({'success': True, 'message': 'Draft saved successfully'})
                    flash(gettext('Draft saved successfully'), 'success')
                else:
                    flash(gettext('Outgoing message created successfully'), 'success')

                return redirect(url_for('messages.view_message', id=message.id))

            except Exception as e:
                db.session.rollback()
                if request.is_json:
                    return jsonify({'success': False, 'message': str(e)}), 500
                flash(gettext('Error creating message: %(error)s', error=str(e)), 'error')

        # GET request - show form
        users = User.query.filter_by(is_active=True).all()
        managers = User.query.filter(User.role.in_([UserRole.ADMIN, UserRole.MANAGER])).all()
        organizations = Organization.query.filter_by(is_active=True).all()
        templates = LetterTemplate.query.filter_by(is_active=True).all()
        signatures = DigitalSignature.query.filter_by(user_id=current_user.id, is_active=True).all()
        stamps = OfficialStamp.query.filter_by(is_active=True).all()

        # Generate next message number for preview
        next_message_number = generate_message_number(MessageType.OUTGOING)

        return render_template('messages/create_outgoing.html',
                             users=users,
                             managers=managers,
                             organizations=organizations,
                             templates=templates,
                             signatures=signatures,
                             stamps=stamps,
                             next_message_number=next_message_number)

    @messages_bp.route('/add-organization', methods=['POST'])
    @login_required
    def add_organization():
        try:
            organization = Organization(
                name=request.form['name'],
                name_ar=request.form.get('name_ar', ''),
                type=request.form.get('type', 'private'),
                email=request.form.get('email', ''),
                phone=request.form.get('phone', ''),
                contact_person=request.form.get('contact_person', ''),
                address=request.form.get('address', '')
            )

            db.session.add(organization)
            db.session.commit()

            return jsonify({
                'success': True,
                'organization': {
                    'id': organization.id,
                    'name': organization.name,
                    'name_ar': organization.name_ar
                }
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500
    
    @messages_bp.route('/view/<int:id>')
    @login_required
    def view_message(id):
        message = Message.query.get_or_404(id)
        # Check permissions
        if not (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or
                message.sender_id == current_user.id or
                message.recipient_id == current_user.id):
            flash(gettext('You do not have permission to view this message'), 'error')
            return redirect(url_for('dashboard'))

        workflows = Workflow.query.filter_by(message_id=id).order_by(Workflow.created_at.desc()).all()
        return render_template('messages/view.html', message=message, workflows=workflows)

    @messages_bp.route('/forward/<int:id>', methods=['GET', 'POST'])
    @login_required
    def forward_message(id):
        message = Message.query.get_or_404(id)
        if request.method == 'POST':
            recipient_id = int(request.form['recipient_id'])
            notes = request.form.get('notes', '')

            workflow = Workflow(
                message_id=id,
                from_user_id=current_user.id,
                to_user_id=recipient_id,
                action='forwarded',
                notes=notes
            )
            db.session.add(workflow)

            # Update message recipient
            message.recipient_id = recipient_id
            message.status = MessageStatus.IN_PROGRESS
            db.session.commit()

            flash(gettext('Message forwarded successfully'), 'success')
            return redirect(url_for('messages.view_message', id=id))

        users = User.query.filter_by(is_active=True).all()
        return render_template('messages/forward.html', message=message, users=users)

    @messages_bp.route('/search')
    @login_required
    def search():
        query = request.args.get('q', '')
        message_type = request.args.get('type', '')
        status = request.args.get('status', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        messages_query = Message.query

        if query:
            messages_query = messages_query.filter(
                db.or_(
                    Message.subject.contains(query),
                    Message.subject_ar.contains(query),
                    Message.content.contains(query),
                    Message.content_ar.contains(query),
                    Message.message_number.contains(query)
                )
            )

        if message_type:
            messages_query = messages_query.filter_by(message_type=MessageType(message_type))

        if status:
            messages_query = messages_query.filter_by(status=MessageStatus(status))

        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            messages_query = messages_query.filter(Message.created_at >= date_from_obj)

        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            messages_query = messages_query.filter(Message.created_at <= date_to_obj)

        page = request.args.get('page', 1, type=int)
        messages = messages_query.order_by(Message.created_at.desc())\
                                .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])

        return render_template('messages/search.html', messages=messages,
                             query=query, message_type=message_type, status=status,
                             date_from=date_from, date_to=date_to)

    @messages_bp.route('/preview-letter', methods=['POST'])
    @login_required
    def preview_letter():
        try:
            # Create temporary message object for preview
            temp_message = {
                'subject': request.form.get('subject', ''),
                'subject_ar': request.form.get('subject_ar', ''),
                'content': request.form.get('content', ''),
                'content_ar': request.form.get('content_ar', ''),
                'letter_type': request.form.get('letter_type', ''),
                'reference_number': request.form.get('reference_number', ''),
                'priority': request.form.get('priority', 'medium'),
                'sender': current_user,
                'created_at': datetime.utcnow()
            }

            # Get recipient info
            recipient_type = request.form.get('recipient_type', 'internal')
            if recipient_type == 'internal':
                recipient_id = request.form.get('recipient_id')
                if recipient_id:
                    temp_message['recipient'] = User.query.get(recipient_id)
            elif recipient_type == 'organization':
                org_id = request.form.get('organization_id')
                if org_id:
                    temp_message['organization'] = Organization.query.get(org_id)
                temp_message['external_recipient'] = request.form.get('org_contact_person', '')
            elif recipient_type == 'external':
                temp_message['external_recipient'] = request.form.get('external_recipient', '')

            # Get template
            template_id = request.form.get('letter_template_id')
            if template_id:
                temp_message['template'] = LetterTemplate.query.get(template_id)

            # Generate preview HTML
            preview_html = generate_letter_preview(temp_message)
            return preview_html

        except Exception as e:
            return f"<html><body><h3>Error generating preview</h3><p>{str(e)}</p></body></html>"

    @messages_bp.route('/generate-document/<int:message_id>')
    @login_required
    def generate_document(message_id):
        message = Message.query.get_or_404(message_id)

        # Check permissions
        if not (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or
                message.sender_id == current_user.id or
                message.recipient_id == current_user.id):
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        try:
            document_path = generate_letter_document(message)
            message.generated_document_path = document_path
            db.session.commit()

            flash(gettext('Document generated successfully'), 'success')
            return redirect(url_for('messages.view_message', id=message_id))

        except Exception as e:
            flash(gettext('Error generating document: %(error)s', error=str(e)), 'error')
            return redirect(url_for('messages.view_message', id=message_id))

    @messages_bp.route('/download-document/<int:message_id>')
    @login_required
    def download_document(message_id):
        message = Message.query.get_or_404(message_id)

        # Check permissions
        if not (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or
                message.sender_id == current_user.id or
                message.recipient_id == current_user.id):
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        if not message.generated_document_path or not os.path.exists(message.generated_document_path):
            flash(gettext('Document not found. Please generate the document first.'), 'error')
            return redirect(url_for('messages.view_message', id=message_id))

        try:
            filename = f"letter_{message.message_number}.docx"
            return send_file(message.generated_document_path, as_attachment=True, download_name=filename)
        except Exception as e:
            flash(gettext('Error downloading document: %(error)s', error=str(e)), 'error')
            return redirect(url_for('messages.view_message', id=message_id))

    @messages_bp.route('/print-document/<int:id>')
    @login_required
    def print_document(id):
        message = Message.query.get_or_404(id)

        # Check permissions
        if not (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or
                message.sender_id == current_user.id or
                message.recipient_id == current_user.id):
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        try:
            # Generate print-friendly HTML
            print_html = generate_print_version(message)

            # Update print tracking
            message.is_printed = True
            message.printed_at = datetime.utcnow()
            message.printed_by = current_user.id
            db.session.commit()

            # Return HTML for printing
            return print_html

        except Exception as e:
            flash(gettext('Error generating print version: %(error)s', error=str(e)), 'error')
            return redirect(url_for('messages.view_message', id=id))

    app.register_blueprint(messages_bp, url_prefix='/messages')

    # File download route
    @app.route('/download/<int:id>')
    @login_required
    def download_attachment(id):
        attachment = Attachment.query.get_or_404(id)
        message = attachment.message

        # Check permissions
        if not (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or
                message.sender_id == current_user.id or
                message.recipient_id == current_user.id):
            flash(gettext('You do not have permission to download this file'), 'error')
            return redirect(url_for('dashboard'))

        return send_file(attachment.file_path,
                        as_attachment=True,
                        download_name=attachment.original_filename)

    # Admin Blueprint
    admin_bp = Blueprint('admin', __name__)

    @admin_bp.route('/templates-signatures')
    @login_required
    def templates_signatures():
        # Check admin permissions
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        templates = LetterTemplate.query.all()
        signatures = DigitalSignature.query.all()
        stamps = OfficialStamp.query.all()
        organizations = Organization.query.all()

        return render_template('admin/templates_signatures.html',
                             templates=templates,
                             signatures=signatures,
                             stamps=stamps,
                             organizations=organizations)

    @admin_bp.route('/panel')
    @login_required
    def panel():
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        # Statistics
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_messages = Message.query.count()
        pending_messages = Message.query.filter_by(status=MessageStatus.PENDING).count()

        return render_template('admin/panel.html',
                             total_users=total_users,
                             active_users=active_users,
                             total_messages=total_messages,
                             pending_messages=pending_messages)

    @admin_bp.route('/users')
    @login_required
    def users():
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        page = request.args.get('page', 1, type=int)
        users_pagination = User.query.order_by(User.created_at.desc())\
                         .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])
        return render_template('admin/users.html', users=users_pagination.items, pagination=users_pagination)

    @admin_bp.route('/users/create', methods=['GET', 'POST'])
    @login_required
    def create_user():
        if current_user.role != UserRole.ADMIN:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            user = User(
                username=request.form['username'],
                email=request.form['email'],
                full_name=request.form['full_name'],
                full_name_ar=request.form.get('full_name_ar', ''),
                role=UserRole(request.form['role']),
                department=request.form.get('department', ''),
                department_ar=request.form.get('department_ar', ''),
                phone=request.form.get('phone', ''),
                preferred_language=request.form.get('preferred_language', 'en')
            )
            user.set_password(request.form['password'])

            try:
                db.session.add(user)
                db.session.commit()
                flash(gettext('User created successfully'), 'success')
                return redirect(url_for('admin.users'))
            except Exception as e:
                db.session.rollback()
                flash(gettext('Error creating user: %(error)s', error=str(e)), 'error')

        return render_template('admin/create_user.html')

    @admin_bp.route('/users/<int:user_id>')
    @login_required
    def get_user(user_id):
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            return jsonify({'error': 'Access denied'}), 403

        user = User.query.get_or_404(user_id)
        return jsonify({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'full_name_ar': user.full_name_ar,
            'department': user.department,
            'department_ar': user.department_ar,
            'role': user.role.value,
            'is_active': user.is_active
        })

    @admin_bp.route('/users/<int:user_id>/edit', methods=['POST'])
    @login_required
    def edit_user(user_id):
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        user = User.query.get_or_404(user_id)

        # Only admin can edit other admins
        if user.role == UserRole.ADMIN and current_user.role != UserRole.ADMIN:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('admin.users'))

        user.username = request.form['username']
        user.email = request.form['email']
        user.full_name = request.form['full_name']
        user.full_name_ar = request.form.get('full_name_ar', '')
        user.department = request.form['department']
        user.department_ar = request.form.get('department_ar', '')

        # Only admin can change roles
        if current_user.role == UserRole.ADMIN:
            user.role = UserRole(request.form['role'])

        # Update password if provided
        if request.form.get('password'):
            user.password_hash = generate_password_hash(request.form['password'])

        try:
            db.session.commit()
            flash(gettext('User updated successfully'), 'success')
        except Exception as e:
            db.session.rollback()
            flash(gettext('Error updating user: %(error)s', error=str(e)), 'error')

        return redirect(url_for('admin.users'))

    @admin_bp.route('/users/<int:user_id>/toggle', methods=['POST'])
    @login_required
    def toggle_user_status(user_id):
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        user = User.query.get_or_404(user_id)

        # Cannot deactivate yourself
        if user.id == current_user.id:
            return jsonify({'success': False, 'message': gettext('Cannot change your own status')}), 400

        # Only admin can deactivate other admins
        if user.role == UserRole.ADMIN and current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': gettext('Access denied')}), 403

        user.is_active = not user.is_active

        try:
            db.session.commit()
            return jsonify({'success': True})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500

    @admin_bp.route('/users/<int:user_id>/delete', methods=['POST'])
    @login_required
    def delete_user(user_id):
        if current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        user = User.query.get_or_404(user_id)

        # Cannot delete yourself
        if user.id == current_user.id:
            return jsonify({'success': False, 'message': gettext('Cannot delete yourself')}), 400

        try:
            db.session.delete(user)
            db.session.commit()
            return jsonify({'success': True})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500

    @admin_bp.route('/settings', methods=['GET', 'POST'])
    @login_required
    def settings():
        if current_user.role != UserRole.ADMIN:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            try:
                # Update organization settings
                org_name = request.form.get('org_name')
                org_name_ar = request.form.get('org_name_ar')
                default_language = request.form.get('default_language')
                messages_per_page = request.form.get('messages_per_page')
                session_timeout = request.form.get('session_timeout')
                max_file_size = request.form.get('max_file_size')
                allowed_extensions = request.form.get('allowed_extensions')
                email_notifications = request.form.get('email_notifications') == 'on'
                auto_archive_days = request.form.get('auto_archive_days')
                backup_enabled = request.form.get('backup_enabled') == 'on'
                backup_frequency = request.form.get('backup_frequency')

                # Update or create settings
                settings_data = {
                    'organization_name': org_name,
                    'organization_name_ar': org_name_ar,
                    'default_language': default_language,
                    'messages_per_page': messages_per_page,
                    'session_timeout': session_timeout,
                    'max_file_size': max_file_size,
                    'allowed_extensions': allowed_extensions,
                    'email_notifications': email_notifications,
                    'auto_archive_days': auto_archive_days,
                    'backup_enabled': backup_enabled,
                    'backup_frequency': backup_frequency
                }

                for key, value in settings_data.items():
                    setting = SystemSettings.query.filter_by(key=key).first()
                    if setting:
                        setting.value = str(value)
                        setting.updated_at = datetime.utcnow()
                    else:
                        setting = SystemSettings(
                            key=key,
                            value=str(value),
                            description=f'System setting for {key}'
                        )
                        db.session.add(setting)

                db.session.commit()

                if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': True, 'message': gettext('Settings saved successfully')})
                else:
                    flash(gettext('Settings saved successfully'), 'success')
                    return redirect(url_for('admin.settings'))

            except Exception as e:
                db.session.rollback()
                if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'message': str(e)}), 500
                else:
                    flash(gettext('Error saving settings: %(error)s', error=str(e)), 'error')

        # Get current settings
        current_settings = {}
        settings = SystemSettings.query.all()
        for setting in settings:
            current_settings[setting.key] = setting.value

        # Set defaults if not found
        defaults = {
            'organization_name': app.config.get('ORGANIZATION_NAME', 'Electronic Correspondence System'),
            'organization_name_ar': app.config.get('ORGANIZATION_NAME_AR', 'نظام المراسلات الإلكترونية'),
            'default_language': app.config.get('BABEL_DEFAULT_LOCALE', 'en'),
            'messages_per_page': str(app.config.get('MESSAGES_PER_PAGE', 20)),
            'session_timeout': '8',
            'max_file_size': '16',
            'allowed_extensions': 'pdf,doc,docx,txt,png,jpg,jpeg,gif',
            'email_notifications': 'True',
            'auto_archive_days': '365',
            'backup_enabled': 'True',
            'backup_frequency': 'daily'
        }

        for key, default_value in defaults.items():
            if key not in current_settings:
                current_settings[key] = default_value

        return render_template('admin/settings.html', settings=current_settings)

    @admin_bp.route('/system-stats')
    @login_required
    def system_stats():
        if current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        try:
            total_users = User.query.count()
            total_messages = Message.query.count()

            # Get database size
            db_path = app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')
            db_size = "Unknown"
            if os.path.exists(db_path):
                size_bytes = os.path.getsize(db_path)
                if size_bytes < 1024:
                    db_size = f"{size_bytes} B"
                elif size_bytes < 1024 * 1024:
                    db_size = f"{size_bytes / 1024:.1f} KB"
                else:
                    db_size = f"{size_bytes / (1024 * 1024):.1f} MB"

            # Get last backup date
            last_backup = SystemSettings.query.filter_by(key='last_backup_date').first()
            last_backup_date = last_backup.value if last_backup else None

            return jsonify({
                'success': True,
                'total_users': total_users,
                'total_messages': total_messages,
                'db_size': db_size,
                'last_backup': last_backup_date
            })

        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    @admin_bp.route('/create-backup', methods=['POST'])
    @login_required
    def create_backup():
        if current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        try:
            import shutil
            from datetime import datetime

            # Create backups directory if it doesn't exist
            backup_dir = 'backups'
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            # Get database path
            db_path = app.config.get('SQLALCHEMY_DATABASE_URI', '').replace('sqlite:///', '')

            if not os.path.exists(db_path):
                return jsonify({'success': False, 'message': 'Database file not found'}), 404

            # Create backup filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'backup_{timestamp}.db'
            backup_path = os.path.join(backup_dir, backup_filename)

            # Copy database file
            shutil.copy2(db_path, backup_path)

            # Update last backup date in settings
            last_backup_setting = SystemSettings.query.filter_by(key='last_backup_date').first()
            if last_backup_setting:
                last_backup_setting.value = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                last_backup_setting.updated_at = datetime.utcnow()
            else:
                last_backup_setting = SystemSettings(
                    key='last_backup_date',
                    value=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    description='Last backup creation date'
                )
                db.session.add(last_backup_setting)

            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Backup created successfully',
                'filename': backup_filename
            })

        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    @admin_bp.route('/backup-history')
    @login_required
    def backup_history():
        if current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        try:
            backup_dir = 'backups'
            backups = []

            if os.path.exists(backup_dir):
                for filename in os.listdir(backup_dir):
                    if filename.endswith('.db'):
                        file_path = os.path.join(backup_dir, filename)
                        stat = os.stat(file_path)
                        size_mb = stat.st_size / (1024 * 1024)

                        backups.append({
                            'filename': filename,
                            'date': datetime.fromtimestamp(stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                            'size': f"{size_mb:.1f} MB",
                            'type': 'Manual',
                            'status': 'success'
                        })

            # Sort by date (newest first)
            backups.sort(key=lambda x: x['date'], reverse=True)

            return jsonify({
                'success': True,
                'backups': backups
            })

        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    @admin_bp.route('/download-backup/<filename>')
    @login_required
    def download_backup(filename):
        if current_user.role != UserRole.ADMIN:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        try:
            backup_dir = 'backups'
            file_path = os.path.join(backup_dir, filename)

            if not os.path.exists(file_path) or not filename.endswith('.db'):
                flash(gettext('Backup file not found'), 'error')
                return redirect(url_for('admin.settings'))

            return send_file(file_path, as_attachment=True, download_name=filename)

        except Exception as e:
            flash(gettext('Error downloading backup: %(error)s', error=str(e)), 'error')
            return redirect(url_for('admin.settings'))

    @admin_bp.route('/reset-settings', methods=['POST'])
    @login_required
    def reset_settings():
        if current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        try:
            # Delete all system settings
            SystemSettings.query.delete()
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Settings reset to defaults successfully'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500

    app.register_blueprint(admin_bp, url_prefix='/admin')

    # Profile Blueprint
    profile_bp = Blueprint('profile', __name__)

    @profile_bp.route('/')
    @login_required
    def view_profile():
        # Get user statistics
        sent_messages = Message.query.filter_by(sender_id=current_user.id).count()
        received_messages = Message.query.filter_by(recipient_id=current_user.id).count()
        pending_messages = Message.query.filter(
            (Message.sender_id == current_user.id) | (Message.recipient_id == current_user.id),
            Message.status == MessageStatus.PENDING
        ).count()

        # Get recent activity
        recent_sent = Message.query.filter_by(sender_id=current_user.id)\
                                 .order_by(Message.created_at.desc()).limit(5).all()
        recent_received = Message.query.filter_by(recipient_id=current_user.id)\
                                     .order_by(Message.created_at.desc()).limit(5).all()

        return render_template('profile/view.html',
                             sent_messages=sent_messages,
                             received_messages=received_messages,
                             pending_messages=pending_messages,
                             recent_sent=recent_sent,
                             recent_received=recent_received)

    @profile_bp.route('/edit', methods=['GET', 'POST'])
    @login_required
    def edit_profile():
        if request.method == 'POST':
            try:
                # Update user information
                current_user.full_name = request.form['full_name']
                current_user.full_name_ar = request.form.get('full_name_ar', '')
                current_user.email = request.form['email']
                current_user.phone = request.form.get('phone', '')
                current_user.department = request.form.get('department', '')
                current_user.department_ar = request.form.get('department_ar', '')
                current_user.preferred_language = request.form.get('preferred_language', 'en')

                # Update password if provided
                if request.form.get('current_password') and request.form.get('new_password'):
                    if current_user.check_password(request.form['current_password']):
                        if request.form['new_password'] == request.form['confirm_password']:
                            current_user.set_password(request.form['new_password'])
                        else:
                            flash(gettext('New passwords do not match'), 'error')
                            return render_template('profile/edit.html')
                    else:
                        flash(gettext('Current password is incorrect'), 'error')
                        return render_template('profile/edit.html')

                db.session.commit()
                flash(gettext('Profile updated successfully'), 'success')
                return redirect(url_for('profile.view_profile'))

            except Exception as e:
                db.session.rollback()
                flash(gettext('Error updating profile: %(error)s', error=str(e)), 'error')

        return render_template('profile/edit.html')

    @profile_bp.route('/settings', methods=['GET', 'POST'])
    @login_required
    def user_settings():
        if request.method == 'POST':
            try:
                # Update user preferences
                current_user.preferred_language = request.form.get('preferred_language', 'en')

                # Update notification preferences (we'll add these fields to User model)
                email_notifications = request.form.get('email_notifications') == 'on'
                desktop_notifications = request.form.get('desktop_notifications') == 'on'

                # For now, we'll store these in session or create user preferences table later
                session['email_notifications'] = email_notifications
                session['desktop_notifications'] = desktop_notifications

                db.session.commit()
                flash(gettext('Settings updated successfully'), 'success')
                return redirect(url_for('profile.user_settings'))

            except Exception as e:
                db.session.rollback()
                flash(gettext('Error updating settings: %(error)s', error=str(e)), 'error')

        # Get current settings
        email_notifications = session.get('email_notifications', True)
        desktop_notifications = session.get('desktop_notifications', True)

        return render_template('profile/settings.html',
                             email_notifications=email_notifications,
                             desktop_notifications=desktop_notifications)

    @profile_bp.route('/activity')
    @login_required
    def activity():
        page = request.args.get('page', 1, type=int)

        # Get all messages related to user
        user_messages = Message.query.filter(
            (Message.sender_id == current_user.id) | (Message.recipient_id == current_user.id)
        ).order_by(Message.created_at.desc())\
         .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])

        # Get workflow history
        workflows = Workflow.query.filter(
            (Workflow.from_user_id == current_user.id) | (Workflow.to_user_id == current_user.id)
        ).order_by(Workflow.created_at.desc()).limit(20).all()

        return render_template('profile/activity.html',
                             messages=user_messages.items,
                             pagination=user_messages,
                             workflows=workflows)

    @profile_bp.route('/download-data', methods=['POST'])
    @login_required
    def download_data():
        try:
            import json
            from datetime import datetime

            # Collect user data
            user_data = {
                'user_info': {
                    'username': current_user.username,
                    'email': current_user.email,
                    'full_name': current_user.full_name,
                    'full_name_ar': current_user.full_name_ar,
                    'department': current_user.department,
                    'department_ar': current_user.department_ar,
                    'phone': current_user.phone,
                    'role': current_user.role.value,
                    'preferred_language': current_user.preferred_language,
                    'created_at': current_user.created_at.isoformat() if current_user.created_at else None,
                    'last_login': current_user.last_login.isoformat() if current_user.last_login else None
                },
                'sent_messages': [],
                'received_messages': [],
                'workflows': []
            }

            # Get sent messages
            sent_messages = Message.query.filter_by(sender_id=current_user.id).all()
            for msg in sent_messages:
                user_data['sent_messages'].append({
                    'id': msg.id,
                    'message_number': msg.message_number,
                    'subject': msg.subject,
                    'subject_ar': msg.subject_ar,
                    'content': msg.content,
                    'content_ar': msg.content_ar,
                    'message_type': msg.message_type.value,
                    'status': msg.status.value,
                    'priority': msg.priority.value,
                    'created_at': msg.created_at.isoformat() if msg.created_at else None,
                    'recipient': msg.recipient.full_name if msg.recipient else msg.external_recipient
                })

            # Get received messages
            received_messages = Message.query.filter_by(recipient_id=current_user.id).all()
            for msg in received_messages:
                user_data['received_messages'].append({
                    'id': msg.id,
                    'message_number': msg.message_number,
                    'subject': msg.subject,
                    'subject_ar': msg.subject_ar,
                    'message_type': msg.message_type.value,
                    'status': msg.status.value,
                    'priority': msg.priority.value,
                    'created_at': msg.created_at.isoformat() if msg.created_at else None,
                    'sender': msg.sender.full_name if msg.sender else msg.external_sender
                })

            # Get workflows
            workflows = Workflow.query.filter(
                (Workflow.from_user_id == current_user.id) | (Workflow.to_user_id == current_user.id)
            ).all()
            for workflow in workflows:
                user_data['workflows'].append({
                    'id': workflow.id,
                    'action': workflow.action,
                    'notes': workflow.notes,
                    'created_at': workflow.created_at.isoformat() if workflow.created_at else None,
                    'from_user': workflow.from_user.full_name if workflow.from_user else None,
                    'to_user': workflow.to_user.full_name if workflow.to_user else None
                })

            # Create JSON response
            json_data = json.dumps(user_data, ensure_ascii=False, indent=2)

            response = make_response(json_data)
            response.headers['Content-Type'] = 'application/json'
            response.headers['Content-Disposition'] = f'attachment; filename=user_data_{current_user.username}_{datetime.now().strftime("%Y%m%d")}.json'

            return response

        except Exception as e:
            return jsonify({'success': False, 'message': str(e)}), 500

    @profile_bp.route('/request-deletion', methods=['POST'])
    @login_required
    def request_deletion():
        try:
            # Create a system setting to track deletion requests
            deletion_request = SystemSettings(
                key=f'deletion_request_{current_user.id}',
                value=f'User {current_user.username} requested account deletion',
                description=f'Account deletion request from {current_user.full_name} on {datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")}'
            )
            db.session.add(deletion_request)
            db.session.commit()

            return jsonify({
                'success': True,
                'message': 'Account deletion request submitted successfully'
            })

        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500

    app.register_blueprint(profile_bp, url_prefix='/profile')

    def generate_print_version(message):
        """Generate HTML for printing a message"""
        try:
            # Get system settings for letterhead
            letterhead_setting = SystemSettings.query.filter_by(key='letterhead_template').first()
            letterhead = letterhead_setting.value if letterhead_setting else ""

            # Create print-friendly HTML
            html_content = f"""
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>طباعة الرسالة #{message.message_number}</title>
                <style>
                    @media print {{
                        body {{ margin: 0; }}
                        .no-print {{ display: none !important; }}
                    }}
                    body {{
                        font-family: 'Arial', sans-serif;
                        line-height: 1.6;
                        color: #333;
                        max-width: 800px;
                        margin: 0 auto;
                        padding: 20px;
                    }}
                    .letterhead {{
                        text-align: center;
                        border-bottom: 2px solid #333;
                        padding-bottom: 20px;
                        margin-bottom: 30px;
                    }}
                    .message-header {{
                        margin-bottom: 30px;
                    }}
                    .message-details {{
                        display: grid;
                        grid-template-columns: 1fr 1fr;
                        gap: 15px;
                        margin-bottom: 30px;
                    }}
                    .detail-item {{
                        padding: 10px;
                        background-color: #f8f9fa;
                        border-radius: 5px;
                    }}
                    .message-content {{
                        background-color: #fff;
                        padding: 30px;
                        border: 1px solid #ddd;
                        border-radius: 8px;
                        margin-bottom: 30px;
                        min-height: 200px;
                    }}
                    .signature-section {{
                        margin-top: 50px;
                        text-align: left;
                    }}
                    .print-button {{
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: #007bff;
                        color: white;
                        border: none;
                        padding: 10px 20px;
                        border-radius: 5px;
                        cursor: pointer;
                    }}
                    @media print {{
                        .print-button {{ display: none; }}
                    }}
                </style>
            </head>
            <body>
                <button class="print-button no-print" onclick="window.print()">طباعة</button>

                <div class="letterhead">
                    {letterhead}
                </div>

                <div class="message-header">
                    <h1 style="text-align: center;">رسالة رقم #{message.message_number}</h1>
                </div>

                <div class="message-details">
                    <div class="detail-item">
                        <strong>التاريخ:</strong> {message.created_at.strftime('%Y-%m-%d')}
                    </div>
                    <div class="detail-item">
                        <strong>رقم الرسالة:</strong> {message.message_number}
                    </div>
                    <div class="detail-item">
                        <strong>من:</strong> {message.sender.full_name}
                    </div>
                    <div class="detail-item">
                        <strong>إلى:</strong> {message.recipient.full_name}
                    </div>
                    <div class="detail-item">
                        <strong>الموضوع:</strong> {message.subject}
                    </div>
                    <div class="detail-item">
                        <strong>نوع الوثيقة:</strong> {message.letter_type or message.message_type.value}
                    </div>
                </div>

                <div class="message-content">
                    {message.content.replace(chr(10), '<br>')}
                </div>

                {f'''
                <div class="signature-section">
                    <p><strong>التوقيع:</strong></p>
                    <p>{message.signature.name}</p>
                    {f'<img src="data:image/png;base64,{message.signature.image_data}" style="max-width: 200px; max-height: 100px;">' if message.signature and message.signature.image_data else ''}
                </div>
                ''' if message.signature else ''}

                <script>
                    // Auto-print when page loads (optional)
                    // window.onload = function() {{ window.print(); }}
                </script>
            </body>
            </html>
            """

            return html_content

        except Exception as e:
            print(f"Error generating print version: {e}")
            return f"<html><body><h1>خطأ في إنشاء نسخة الطباعة: {str(e)}</h1></body></html>"

    # Initialize database
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                full_name_ar='مدير النظام',
                role=UserRole.ADMIN,
                department='IT',
                department_ar='تقنية المعلومات'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
