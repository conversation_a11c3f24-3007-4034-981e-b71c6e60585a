from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file, session, Blueprint
from flask_login import Lo<PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_babel import Babel, gettext, ngettext, lazy_gettext, get_locale
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash
from datetime import datetime, timedelta
import os
import uuid
from config import config
from models import db, User, Message, Attachment, Workflow, SystemSettings, UserRole, MessageType, MessageStatus, Priority

# Moment.js-like functionality for templates
class MomentJS:
    def __init__(self, timestamp=None):
        if timestamp is None:
            self.timestamp = datetime.now()
        elif isinstance(timestamp, datetime):
            self.timestamp = timestamp
        else:
            self.timestamp = datetime.fromtimestamp(timestamp)

    def format(self, fmt):
        """Format datetime using Python strftime format"""
        format_map = {
            'DD': '%d',
            'MM': '%m',
            'YYYY': '%Y',
            'MMMM': '%B',
            'MMM': '%b',
            'HH': '%H',
            'mm': '%M',
            'ss': '%S'
        }

        # Replace moment.js format with Python format
        python_fmt = fmt
        for moment_fmt, python_equiv in format_map.items():
            python_fmt = python_fmt.replace(moment_fmt, python_equiv)

        return self.timestamp.strftime(python_fmt)

    @property
    def year(self):
        return self.timestamp.year

def moment(timestamp=None):
    """Create a moment object for template use"""
    return MomentJS(timestamp)

# Language detection function
def get_locale():
    # Check if user manually selected a language
    if 'language' in session:
        return session['language']

    # Check user's preferred language from browser
    return request.accept_languages.best_match(['ar', 'en']) or 'en'

def create_app(config_name='default'):
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialize extensions
    db.init_app(app)

    # Initialize Babel
    babel = Babel(app)
    babel.init_app(app, locale_selector=get_locale)

    # Initialize Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = lazy_gettext('Please log in to access this page.')
    login_manager.login_message_category = 'info'
    
    # Add template globals for translation functions
    app.jinja_env.globals.update(
        gettext=gettext,
        _=gettext,
        get_locale=get_locale,
        config=app.config,
        moment=moment
    )

    # Add template filters
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """Convert newlines to <br> tags"""
        if text:
            return text.replace('\n', '<br>')
        return text
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Create upload directory
    upload_dir = os.path.join(app.root_path, app.config['UPLOAD_FOLDER'])
    if not os.path.exists(upload_dir):
        os.makedirs(upload_dir)
    
    # Helper functions
    def allowed_file(filename):
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']
    
    def generate_message_number(message_type):
        """Generate unique message number based on type and date"""
        today = datetime.now()
        year = today.year
        month = today.month
        
        # Count messages of this type for this month
        count = Message.query.filter(
            Message.message_type == message_type,
            db.extract('year', Message.created_at) == year,
            db.extract('month', Message.created_at) == month
        ).count() + 1
        
        type_prefix = {
            MessageType.INCOMING: 'IN',
            MessageType.OUTGOING: 'OUT',
            MessageType.INTERNAL: 'INT'
        }
        
        return f"{type_prefix[message_type]}-{year}-{month:02d}-{count:04d}"
    
    # Language route
    @app.route('/set_language/<language>')
    def set_language(language=None):
        if language in app.config['LANGUAGES']:
            session['language'] = language
        return redirect(request.referrer or url_for('index'))

    # Routes
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard'))
        return redirect(url_for('auth.login'))
    

    
    @app.route('/dashboard')
    @login_required
    def dashboard():
        # Get statistics for dashboard
        total_messages = Message.query.count()
        pending_messages = Message.query.filter_by(status=MessageStatus.PENDING).count()
        my_messages = Message.query.filter_by(recipient_id=current_user.id).count()
        
        # Recent messages
        recent_messages = Message.query.filter_by(recipient_id=current_user.id)\
                                     .order_by(Message.created_at.desc())\
                                     .limit(5).all()
        
        # Pending workflows
        pending_workflows = Workflow.query.filter_by(to_user_id=current_user.id, is_read=False)\
                                         .order_by(Workflow.created_at.desc())\
                                         .limit(5).all()
        
        return render_template('dashboard.html',
                             total_messages=total_messages,
                             pending_messages=pending_messages,
                             my_messages=my_messages,
                             recent_messages=recent_messages,
                             pending_workflows=pending_workflows)
    
    # Authentication Blueprint
    auth = Blueprint('auth', __name__)
    
    @auth.route('/login', methods=['GET', 'POST'])
    def login():
        if request.method == 'POST':
            username = request.form['username']
            password = request.form['password']
            remember = bool(request.form.get('remember'))
            
            user = User.query.filter_by(username=username).first()
            
            if user and user.check_password(password) and user.is_active:
                login_user(user, remember=remember)
                user.last_login = datetime.utcnow()
                db.session.commit()
                
                next_page = request.args.get('next')
                if not next_page or not next_page.startswith('/'):
                    next_page = url_for('dashboard')
                return redirect(next_page)
            else:
                flash(gettext('Invalid username or password'), 'error')
        
        return render_template('auth/login.html')
    
    @auth.route('/logout')
    @login_required
    def logout():
        logout_user()
        flash(gettext('You have been logged out successfully'), 'success')
        return redirect(url_for('auth.login'))
    
    app.register_blueprint(auth, url_prefix='/auth')
    
    # Messages Blueprint
    messages_bp = Blueprint('messages', __name__)
    
    @messages_bp.route('/incoming')
    @login_required
    def incoming():
        page = request.args.get('page', 1, type=int)
        messages = Message.query.filter_by(message_type=MessageType.INCOMING)\
                               .order_by(Message.created_at.desc())\
                               .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])
        return render_template('messages/incoming.html', messages=messages)
    
    @messages_bp.route('/outgoing')
    @login_required
    def outgoing():
        page = request.args.get('page', 1, type=int)
        messages = Message.query.filter_by(message_type=MessageType.OUTGOING)\
                               .order_by(Message.created_at.desc())\
                               .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])
        return render_template('messages/outgoing.html', messages=messages)
    
    @messages_bp.route('/create/<message_type>', methods=['GET', 'POST'])
    @login_required
    def create_message(message_type):
        if request.method == 'POST':
            # Create new message
            message = Message(
                message_number=generate_message_number(MessageType(message_type)),
                subject=request.form['subject'],
                subject_ar=request.form.get('subject_ar', ''),
                content=request.form['content'],
                content_ar=request.form.get('content_ar', ''),
                message_type=MessageType(message_type),
                sender_id=current_user.id,
                priority=Priority(request.form.get('priority', 'medium')),
                external_sender=request.form.get('external_sender', ''),
                external_recipient=request.form.get('external_recipient', ''),
                reference_number=request.form.get('reference_number', ''),
                notes=request.form.get('notes', ''),
                is_confidential=bool(request.form.get('is_confidential'))
            )
            
            # Set recipient if internal message
            if request.form.get('recipient_id'):
                message.recipient_id = int(request.form['recipient_id'])
            
            # Set dates
            if request.form.get('received_date'):
                message.received_date = datetime.strptime(request.form['received_date'], '%Y-%m-%d')
            if request.form.get('due_date'):
                message.due_date = datetime.strptime(request.form['due_date'], '%Y-%m-%d')
            
            db.session.add(message)
            db.session.commit()
            
            # Handle file uploads
            if 'attachments' in request.files:
                files = request.files.getlist('attachments')
                for file in files:
                    if file and file.filename and allowed_file(file.filename):
                        filename = secure_filename(file.filename)
                        unique_filename = f"{uuid.uuid4()}_{filename}"
                        file_path = os.path.join(upload_dir, unique_filename)
                        file.save(file_path)
                        
                        attachment = Attachment(
                            filename=unique_filename,
                            original_filename=filename,
                            file_path=file_path,
                            file_size=os.path.getsize(file_path),
                            file_type=filename.rsplit('.', 1)[1].lower(),
                            uploaded_by=current_user.id,
                            message_id=message.id
                        )
                        db.session.add(attachment)
            
            db.session.commit()
            flash(gettext('Message created successfully'), 'success')
            return redirect(url_for('messages.view_message', id=message.id))
        
        # Get users for recipient selection
        users = User.query.filter_by(is_active=True).all()
        return render_template('messages/create.html', message_type=message_type, users=users)
    
    @messages_bp.route('/view/<int:id>')
    @login_required
    def view_message(id):
        message = Message.query.get_or_404(id)
        # Check permissions
        if not (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or
                message.sender_id == current_user.id or
                message.recipient_id == current_user.id):
            flash(gettext('You do not have permission to view this message'), 'error')
            return redirect(url_for('dashboard'))

        workflows = Workflow.query.filter_by(message_id=id).order_by(Workflow.created_at.desc()).all()
        return render_template('messages/view.html', message=message, workflows=workflows)

    @messages_bp.route('/forward/<int:id>', methods=['GET', 'POST'])
    @login_required
    def forward_message(id):
        message = Message.query.get_or_404(id)
        if request.method == 'POST':
            recipient_id = int(request.form['recipient_id'])
            notes = request.form.get('notes', '')

            workflow = Workflow(
                message_id=id,
                from_user_id=current_user.id,
                to_user_id=recipient_id,
                action='forwarded',
                notes=notes
            )
            db.session.add(workflow)

            # Update message recipient
            message.recipient_id = recipient_id
            message.status = MessageStatus.IN_PROGRESS
            db.session.commit()

            flash(gettext('Message forwarded successfully'), 'success')
            return redirect(url_for('messages.view_message', id=id))

        users = User.query.filter_by(is_active=True).all()
        return render_template('messages/forward.html', message=message, users=users)

    @messages_bp.route('/search')
    @login_required
    def search():
        query = request.args.get('q', '')
        message_type = request.args.get('type', '')
        status = request.args.get('status', '')
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')

        messages_query = Message.query

        if query:
            messages_query = messages_query.filter(
                db.or_(
                    Message.subject.contains(query),
                    Message.subject_ar.contains(query),
                    Message.content.contains(query),
                    Message.content_ar.contains(query),
                    Message.message_number.contains(query)
                )
            )

        if message_type:
            messages_query = messages_query.filter_by(message_type=MessageType(message_type))

        if status:
            messages_query = messages_query.filter_by(status=MessageStatus(status))

        if date_from:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d')
            messages_query = messages_query.filter(Message.created_at >= date_from_obj)

        if date_to:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d')
            messages_query = messages_query.filter(Message.created_at <= date_to_obj)

        page = request.args.get('page', 1, type=int)
        messages = messages_query.order_by(Message.created_at.desc())\
                                .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])

        return render_template('messages/search.html', messages=messages,
                             query=query, message_type=message_type, status=status,
                             date_from=date_from, date_to=date_to)

    app.register_blueprint(messages_bp, url_prefix='/messages')

    # File download route
    @app.route('/download/<int:id>')
    @login_required
    def download_attachment(id):
        attachment = Attachment.query.get_or_404(id)
        message = attachment.message

        # Check permissions
        if not (current_user.role in [UserRole.ADMIN, UserRole.MANAGER] or
                message.sender_id == current_user.id or
                message.recipient_id == current_user.id):
            flash(gettext('You do not have permission to download this file'), 'error')
            return redirect(url_for('dashboard'))

        return send_file(attachment.file_path,
                        as_attachment=True,
                        download_name=attachment.original_filename)

    # Admin Blueprint
    admin_bp = Blueprint('admin', __name__)

    @admin_bp.route('/panel')
    @login_required
    def panel():
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        # Statistics
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_messages = Message.query.count()
        pending_messages = Message.query.filter_by(status=MessageStatus.PENDING).count()

        return render_template('admin/panel.html',
                             total_users=total_users,
                             active_users=active_users,
                             total_messages=total_messages,
                             pending_messages=pending_messages)

    @admin_bp.route('/users')
    @login_required
    def users():
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        page = request.args.get('page', 1, type=int)
        users_pagination = User.query.order_by(User.created_at.desc())\
                         .paginate(page=page, per_page=app.config['MESSAGES_PER_PAGE'])
        return render_template('admin/users.html', users=users_pagination.items, pagination=users_pagination)

    @admin_bp.route('/users/create', methods=['GET', 'POST'])
    @login_required
    def create_user():
        if current_user.role != UserRole.ADMIN:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            user = User(
                username=request.form['username'],
                email=request.form['email'],
                full_name=request.form['full_name'],
                full_name_ar=request.form.get('full_name_ar', ''),
                role=UserRole(request.form['role']),
                department=request.form.get('department', ''),
                department_ar=request.form.get('department_ar', ''),
                phone=request.form.get('phone', ''),
                preferred_language=request.form.get('preferred_language', 'en')
            )
            user.set_password(request.form['password'])

            try:
                db.session.add(user)
                db.session.commit()
                flash(gettext('User created successfully'), 'success')
                return redirect(url_for('admin.users'))
            except Exception as e:
                db.session.rollback()
                flash(gettext('Error creating user: %(error)s', error=str(e)), 'error')

        return render_template('admin/create_user.html')

    @admin_bp.route('/users/<int:user_id>')
    @login_required
    def get_user(user_id):
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            return jsonify({'error': 'Access denied'}), 403

        user = User.query.get_or_404(user_id)
        return jsonify({
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'full_name': user.full_name,
            'full_name_ar': user.full_name_ar,
            'department': user.department,
            'department_ar': user.department_ar,
            'role': user.role.value,
            'is_active': user.is_active
        })

    @admin_bp.route('/users/<int:user_id>/edit', methods=['POST'])
    @login_required
    def edit_user(user_id):
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('dashboard'))

        user = User.query.get_or_404(user_id)

        # Only admin can edit other admins
        if user.role == UserRole.ADMIN and current_user.role != UserRole.ADMIN:
            flash(gettext('Access denied'), 'error')
            return redirect(url_for('admin.users'))

        user.username = request.form['username']
        user.email = request.form['email']
        user.full_name = request.form['full_name']
        user.full_name_ar = request.form.get('full_name_ar', '')
        user.department = request.form['department']
        user.department_ar = request.form.get('department_ar', '')

        # Only admin can change roles
        if current_user.role == UserRole.ADMIN:
            user.role = UserRole(request.form['role'])

        # Update password if provided
        if request.form.get('password'):
            user.password_hash = generate_password_hash(request.form['password'])

        try:
            db.session.commit()
            flash(gettext('User updated successfully'), 'success')
        except Exception as e:
            db.session.rollback()
            flash(gettext('Error updating user: %(error)s', error=str(e)), 'error')

        return redirect(url_for('admin.users'))

    @admin_bp.route('/users/<int:user_id>/toggle', methods=['POST'])
    @login_required
    def toggle_user_status(user_id):
        if current_user.role not in [UserRole.ADMIN, UserRole.MANAGER]:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        user = User.query.get_or_404(user_id)

        # Cannot deactivate yourself
        if user.id == current_user.id:
            return jsonify({'success': False, 'message': gettext('Cannot change your own status')}), 400

        # Only admin can deactivate other admins
        if user.role == UserRole.ADMIN and current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': gettext('Access denied')}), 403

        user.is_active = not user.is_active

        try:
            db.session.commit()
            return jsonify({'success': True})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500

    @admin_bp.route('/users/<int:user_id>/delete', methods=['POST'])
    @login_required
    def delete_user(user_id):
        if current_user.role != UserRole.ADMIN:
            return jsonify({'success': False, 'message': 'Access denied'}), 403

        user = User.query.get_or_404(user_id)

        # Cannot delete yourself
        if user.id == current_user.id:
            return jsonify({'success': False, 'message': gettext('Cannot delete yourself')}), 400

        try:
            db.session.delete(user)
            db.session.commit()
            return jsonify({'success': True})
        except Exception as e:
            db.session.rollback()
            return jsonify({'success': False, 'message': str(e)}), 500

    app.register_blueprint(admin_bp, url_prefix='/admin')

    # Initialize database
    with app.app_context():
        db.create_all()

        # Create default admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                full_name='System Administrator',
                full_name_ar='مدير النظام',
                role=UserRole.ADMIN,
                department='IT',
                department_ar='تقنية المعلومات'
            )
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()

    return app

if __name__ == '__main__':
    app = create_app()
    app.run(debug=True, host='0.0.0.0', port=5000)
