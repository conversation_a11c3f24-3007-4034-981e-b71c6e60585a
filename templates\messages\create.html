{% extends "base.html" %}

{% block title %}{{ _('Create %(type)s Message', type=_(message_type.title())) }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-{{ 'inbox' if message_type == 'incoming' else 'paper-plane' }} me-2"></i>
                {{ _('Create %(type)s Message', type=_(message_type.title())) }}
            </h2>
            <a href="{{ url_for('messages.' + message_type) }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i>{{ _('Back to List') }}
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>{{ _('Message Details') }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subject" class="form-label">{{ _('Subject') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="subject" name="subject" required
                                       placeholder="{{ _('Enter message subject') }}">
                                <div class="invalid-feedback">
                                    {{ _('Please provide a subject.') }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="subject_ar" class="form-label">{{ _('Subject (Arabic)') }}</label>
                                <input type="text" class="form-control" id="subject_ar" name="subject_ar"
                                       placeholder="{{ _('Enter Arabic subject') }}" dir="rtl">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="priority" class="form-label">{{ _('Priority') }}</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low">{{ _('Low') }}</option>
                                    <option value="medium" selected>{{ _('Medium') }}</option>
                                    <option value="high">{{ _('High') }}</option>
                                    <option value="urgent">{{ _('Urgent') }}</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="reference_number" class="form-label">{{ _('Reference Number') }}</label>
                                <input type="text" class="form-control" id="reference_number" name="reference_number"
                                       placeholder="{{ _('Enter reference number') }}">
                            </div>
                        </div>
                        
                        <!-- Sender/Recipient Information -->
                        {% if message_type == 'incoming' %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="external_sender" class="form-label">{{ _('External Sender') }}</label>
                                <input type="text" class="form-control" id="external_sender" name="external_sender"
                                       placeholder="{{ _('Enter sender name/organization') }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recipient_id" class="form-label">{{ _('Assign To') }}</label>
                                <select class="form-select" id="recipient_id" name="recipient_id">
                                    <option value="">{{ _('Select recipient') }}</option>
                                    {% for user in users %}
                                    <option value="{{ user.id }}">
                                        {{ user.full_name_ar if get_locale() == 'ar' and user.full_name_ar else user.full_name }}
                                        ({{ user.department_ar if get_locale() == 'ar' and user.department_ar else user.department }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        {% else %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="external_recipient" class="form-label">{{ _('External Recipient') }}</label>
                                <input type="text" class="form-control" id="external_recipient" name="external_recipient"
                                       placeholder="{{ _('Enter recipient name/organization') }}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="recipient_id" class="form-label">{{ _('Internal Recipient') }}</label>
                                <select class="form-select" id="recipient_id" name="recipient_id">
                                    <option value="">{{ _('Select internal recipient') }}</option>
                                    {% for user in users %}
                                    <option value="{{ user.id }}">
                                        {{ user.full_name_ar if get_locale() == 'ar' and user.full_name_ar else user.full_name }}
                                        ({{ user.department_ar if get_locale() == 'ar' and user.department_ar else user.department }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Dates -->
                        {% if message_type == 'incoming' %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="received_date" class="form-label">{{ _('Received Date') }}</label>
                                <input type="date" class="form-control" id="received_date" name="received_date"
                                       value="{{ moment().format('YYYY-MM-DD') }}">
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="due_date" class="form-label">{{ _('Due Date') }}</label>
                                <input type="date" class="form-control" id="due_date" name="due_date">
                            </div>
                        </div>
                        
                        <!-- Content -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="content" class="form-label">{{ _('Content') }} <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="content" name="content" rows="6" required
                                          placeholder="{{ _('Enter message content') }}"></textarea>
                                <div class="invalid-feedback">
                                    {{ _('Please provide message content.') }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="content_ar" class="form-label">{{ _('Content (Arabic)') }}</label>
                                <textarea class="form-control" id="content_ar" name="content_ar" rows="6"
                                          placeholder="{{ _('Enter Arabic content') }}" dir="rtl"></textarea>
                            </div>
                        </div>
                        
                        <!-- Notes -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="notes" class="form-label">{{ _('Notes') }}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"
                                          placeholder="{{ _('Enter additional notes') }}"></textarea>
                            </div>
                        </div>
                        
                        <!-- Attachments -->
                        <div class="col-12">
                            <div class="mb-3">
                                <label for="attachments" class="form-label">{{ _('Attachments') }}</label>
                                <input type="file" class="form-control" id="attachments" name="attachments" multiple
                                       accept=".pdf,.doc,.docx,.txt,.png,.jpg,.jpeg,.gif">
                                <div class="form-text">
                                    {{ _('Allowed file types: PDF, Word, Text, Images. Maximum size: 16MB per file.') }}
                                </div>
                                <div id="attachments_preview" class="mt-2"></div>
                            </div>
                        </div>
                        
                        <!-- Options -->
                        <div class="col-12">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_confidential" name="is_confidential">
                                    <label class="form-check-label" for="is_confidential">
                                        <i class="fas fa-lock me-1"></i>{{ _('Mark as Confidential') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <hr>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                                        <i class="fas fa-times me-1"></i>{{ _('Cancel') }}
                                    </button>
                                </div>
                                <div>
                                    <button type="submit" name="action" value="draft" class="btn btn-outline-primary me-2">
                                        <i class="fas fa-save me-1"></i>{{ _('Save as Draft') }}
                                    </button>
                                    <button type="submit" name="action" value="create" class="btn btn-primary">
                                        <i class="fas fa-check me-1"></i>{{ _('Create Message') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-resize textareas
document.querySelectorAll('textarea').forEach(function(textarea) {
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = this.scrollHeight + 'px';
    });
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}
