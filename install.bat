@echo off
echo ============================================================
echo Electronic Correspondence System - Windows Installation
echo نظام المراسلات الإلكترونية - التثبيت على ويندوز
echo ============================================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

echo Python found. Running installation script...
python install.py

if errorlevel 1 (
    echo.
    echo Installation failed. Please check the error messages above.
    pause
    exit /b 1
)

echo.
echo Installation completed successfully!
echo.
echo To start the system, run: start.bat
echo Or manually run: python run.py
echo.
pause
