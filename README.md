# Electronic Correspondence System / نظام المراسلات الإلكترونية

A comprehensive web-based electronic correspondence system built with Python Flask, supporting both Arabic and English languages.

## Features / المميزات

### ✅ Core Features
- **Multilingual Support**: Full Arabic and English interface with RTL/LTR support
- **Web-based**: Works on any modern web browser
- **User Management**: Role-based access control (<PERSON><PERSON>, Manager, Secretary, Employee, Third Party)
- **Message Management**: Complete incoming/outgoing message handling
- **File Attachments**: Support for PDF, Word, images with secure upload
- **Workflow System**: Message forwarding and tracking
- **Search & Filtering**: Advanced search with multiple criteria
- **Reports & Statistics**: Comprehensive dashboard and analytics
- **Responsive Design**: Works on desktop, tablet, and mobile devices

### 🔐 User Roles
- **Admin**: Full system access and user management
- **Manager**: Department oversight and reporting
- **Secretary**: Message processing and coordination  
- **Employee**: Basic message handling
- **Third Party**: Limited external access

### 📨 Message Types
- **Incoming**: External messages received by the organization
- **Outgoing**: Messages sent to external parties
- **Internal**: Inter-departmental communications

## Installation / التثبيت

### Prerequisites / المتطلبات
- Python 3.8 or higher
- pip (Python package installer)
- Modern web browser

### Quick Start / البدء السريع

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd electronic-correspondence-system
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Initialize the database**
   ```bash
   python run.py
   ```
   The database will be created automatically on first run.

4. **Access the system**
   - Open your web browser
   - Navigate to: `http://localhost:5000`
   - Login with default credentials:
     - Username: `admin`
     - Password: `admin123`

## Configuration / الإعدادات

### Environment Variables
Create a `.env` file in the project root:

```env
FLASK_CONFIG=development
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
FLASK_DEBUG=True
SECRET_KEY=your-secret-key-here
DATABASE_URL=sqlite:///correspondence_system.db
```

### Database Configuration
The system uses SQLite by default. For production, you can use:
- PostgreSQL: `postgresql://user:password@localhost/dbname`
- MySQL: `mysql://user:password@localhost/dbname`

## Usage / الاستخدام

### First Time Setup
1. Login with admin credentials
2. Go to Administration Panel
3. Create user accounts for your organization
4. Configure system settings (organization name, logo, etc.)
5. Start creating messages

### Creating Messages
1. **Incoming Messages**:
   - Click "Messages" → "New Incoming"
   - Fill in sender information
   - Add subject and content
   - Attach files if needed
   - Assign to responsible person

2. **Outgoing Messages**:
   - Click "Messages" → "New Outgoing"  
   - Fill in recipient information
   - Add subject and content
   - Attach files if needed
   - Send or save as draft

### Message Workflow
1. Messages can be forwarded between users
2. Each forwarding action is tracked
3. Users receive notifications for new assignments
4. Status updates are logged automatically

### Search and Reports
- Use the search function to find messages by:
  - Message number
  - Subject/content
  - Sender/recipient
  - Date range
  - Status/priority
- Generate reports from the admin panel

## File Structure / هيكل الملفات

```
electronic-correspondence-system/
├── app.py                 # Main Flask application
├── models.py             # Database models
├── config.py             # Configuration settings
├── run.py                # Application runner
├── requirements.txt      # Python dependencies
├── babel.cfg            # Translation configuration
├── templates/           # HTML templates
│   ├── base.html        # Base template
│   ├── dashboard.html   # Dashboard
│   ├── auth/           # Authentication templates
│   ├── messages/       # Message templates
│   └── admin/          # Admin templates
├── static/             # Static files
│   ├── css/           # Stylesheets
│   ├── js/            # JavaScript files
│   └── images/        # Images and icons
├── uploads/           # File uploads directory
└── translations/      # Language files
    ├── en/           # English translations
    └── ar/           # Arabic translations
```

## Database Schema / مخطط قاعدة البيانات

### Main Tables
- **users**: User accounts and profiles
- **messages**: All correspondence records
- **attachments**: File attachments
- **workflows**: Message forwarding history
- **system_settings**: Configuration options

### Key Relationships
- Users can send/receive multiple messages
- Messages can have multiple attachments
- Workflows track message movement between users

## Security Features / الأمان

- Password hashing using bcrypt
- Session management with Flask-Login
- File upload validation and sanitization
- Role-based access control
- CSRF protection with Flask-WTF
- Secure file storage

## Customization / التخصيص

### Adding New Languages
1. Update `LANGUAGES` in `config.py`
2. Create translation files using Babel
3. Add language option to templates

### Theming
- Modify `static/css/style.css` for custom styling
- Update templates for layout changes
- Add custom JavaScript in `static/js/app.js`

### Adding Features
- Create new routes in `app.py`
- Add database models in `models.py`
- Create corresponding templates

## Deployment / النشر

### Development
```bash
python run.py
```

### Production
1. Set `FLASK_CONFIG=production`
2. Use a production WSGI server (Gunicorn, uWSGI)
3. Configure reverse proxy (Nginx, Apache)
4. Set up SSL certificate
5. Configure database backups

### Docker Deployment
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "run.py"]
```

## Troubleshooting / استكشاف الأخطاء

### Common Issues
1. **Database errors**: Check file permissions and disk space
2. **File upload issues**: Verify upload directory permissions
3. **Translation problems**: Ensure Babel is properly configured
4. **Performance issues**: Check database indexes and query optimization

### Logs
- Application logs are printed to console in development
- For production, configure proper logging to files

## Support / الدعم

### Documentation
- Check this README for basic usage
- Review code comments for technical details
- Examine templates for UI customization

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License / الترخيص

This project is licensed under the MIT License. See LICENSE file for details.

## Version History / تاريخ الإصدارات

- **v1.0.0**: Initial release with core features
  - User management
  - Message handling
  - File attachments
  - Multilingual support
  - Basic reporting

---

**Electronic Correspondence System** - Built with ❤️ using Python Flask

For technical support or feature requests, please create an issue in the project repository.
