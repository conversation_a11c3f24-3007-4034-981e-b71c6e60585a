{% extends "base.html" %}

{% block title %}{{ _('Admin Panel') }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-bar me-2"></i>{{ _('Administration Panel') }}
            </h2>
            <div class="text-muted">
                {{ _('System Overview and Management') }}
            </div>
        </div>
    </div>
</div>

<!-- System Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card bg-primary text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_users }}</h4>
                        <p class="card-text">{{ _('Total Users') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <small>{{ active_users }} {{ _('active users') }}</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-success text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ total_messages }}</h4>
                        <p class="card-text">{{ _('Total Messages') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-envelope fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <small>{{ _('All time messages') }}</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-warning text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ pending_messages }}</h4>
                        <p class="card-text">{{ _('Pending Messages') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <small>{{ _('Require attention') }}</small>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card bg-info text-white h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">{{ moment().format('DD') }}</h4>
                        <p class="card-text">{{ _('Today') }}</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <small>{{ moment().format('MMMM YYYY') }}</small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>{{ _('Quick Actions') }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('admin.create_user') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus me-1"></i>{{ _('Add New User') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('admin.users') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-users-cog me-1"></i>{{ _('Manage Users') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('messages.search') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-chart-line me-1"></i>{{ _('View Reports') }}
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-outline-secondary w-100" data-bs-toggle="modal" data-bs-target="#settingsModal">
                            <i class="fas fa-cog me-1"></i>{{ _('System Settings') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity and System Status -->
<div class="row">
    <!-- Recent Activity -->
    <div class="col-md-8 mb-4">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>{{ _('Recent System Activity') }}
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="refreshActivity()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">{{ _('New user registered') }}</h6>
                            <p class="timeline-text text-muted">{{ _('John Doe joined the system') }}</p>
                            <small class="text-muted">{{ _('2 hours ago') }}</small>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">{{ _('Message processed') }}</h6>
                            <p class="timeline-text text-muted">{{ _('Incoming message #IN-2024-01-0001 completed') }}</p>
                            <small class="text-muted">{{ _('4 hours ago') }}</small>
                        </div>
                    </div>
                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="timeline-title">{{ _('System backup') }}</h6>
                            <p class="timeline-text text-muted">{{ _('Daily backup completed successfully') }}</p>
                            <small class="text-muted">{{ _('1 day ago') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Status -->
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-server me-2"></i>{{ _('System Status') }}
                </h5>
            </div>
            <div class="card-body">
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ _('Database') }}</span>
                        <span class="badge bg-success">{{ _('Online') }}</span>
                    </div>
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar bg-success" style="width: 100%"></div>
                    </div>
                </div>
                
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ _('File Storage') }}</span>
                        <span class="badge bg-success">{{ _('Online') }}</span>
                    </div>
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar bg-success" style="width: 85%"></div>
                    </div>
                    <small class="text-muted">{{ _('85% used') }}</small>
                </div>
                
                <div class="status-item mb-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ _('Email Service') }}</span>
                        <span class="badge bg-warning">{{ _('Limited') }}</span>
                    </div>
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar bg-warning" style="width: 60%"></div>
                    </div>
                </div>
                
                <div class="status-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>{{ _('System Load') }}</span>
                        <span class="badge bg-info">{{ _('Normal') }}</span>
                    </div>
                    <div class="progress mt-1" style="height: 4px;">
                        <div class="progress-bar bg-info" style="width: 45%"></div>
                    </div>
                    <small class="text-muted">{{ _('45% CPU usage') }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Settings Modal -->
<div class="modal fade" id="settingsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-cog me-2"></i>{{ _('System Settings') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="settingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="org_name" class="form-label">{{ _('Organization Name') }}</label>
                                <input type="text" class="form-control" id="org_name" name="org_name" 
                                       value="{{ config.ORGANIZATION_NAME }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="org_name_ar" class="form-label">{{ _('Organization Name (Arabic)') }}</label>
                                <input type="text" class="form-control" id="org_name_ar" name="org_name_ar" 
                                       value="{{ config.ORGANIZATION_NAME_AR }}" dir="rtl">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="default_language" class="form-label">{{ _('Default Language') }}</label>
                                <select class="form-select" id="default_language" name="default_language">
                                    <option value="en">English</option>
                                    <option value="ar">العربية</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="messages_per_page" class="form-label">{{ _('Messages Per Page') }}</label>
                                <select class="form-select" id="messages_per_page" name="messages_per_page">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ _('Cancel') }}</button>
                <button type="button" class="btn btn-primary" onclick="saveSettings()">{{ _('Save Changes') }}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -35px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -29px;
    top: 17px;
    width: 2px;
    height: calc(100% + 5px);
    background-color: #dee2e6;
}

.status-item .progress {
    height: 4px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshActivity() {
    // Simulate refresh
    location.reload();
}

function saveSettings() {
    var formData = new FormData(document.getElementById('settingsForm'));
    
    fetch('/admin/settings', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Settings saved successfully', 'success');
            bootstrap.Modal.getInstance(document.getElementById('settingsModal')).hide();
        } else {
            showNotification('Error saving settings', 'error');
        }
    })
    .catch(error => {
        showNotification('Error saving settings', 'error');
        console.error('Settings save error:', error);
    });
}
</script>
{% endblock %}
