{% extends "base.html" %}

{% block title %}{{ _('Search Messages') }} - {{ super() }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-search me-2"></i>{{ _('Search Messages') }}
            </h2>
        </div>
    </div>
</div>

<!-- Advanced Search Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>{{ _('Search Criteria') }}
                </h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <label for="q" class="form-label">{{ _('Search Text') }}</label>
                        <input type="text" class="form-control" id="q" name="q" 
                               value="{{ query }}" placeholder="{{ _('Enter keywords, message number, or content...') }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="type" class="form-label">{{ _('Message Type') }}</label>
                        <select class="form-select" id="type" name="type">
                            <option value="">{{ _('All Types') }}</option>
                            <option value="incoming" {{ 'selected' if message_type == 'incoming' }}>{{ _('Incoming') }}</option>
                            <option value="outgoing" {{ 'selected' if message_type == 'outgoing' }}>{{ _('Outgoing') }}</option>
                            <option value="internal" {{ 'selected' if message_type == 'internal' }}>{{ _('Internal') }}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="status" class="form-label">{{ _('Status') }}</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">{{ _('All Statuses') }}</option>
                            <option value="pending" {{ 'selected' if status == 'pending' }}>{{ _('Pending') }}</option>
                            <option value="in_progress" {{ 'selected' if status == 'in_progress' }}>{{ _('In Progress') }}</option>
                            <option value="completed" {{ 'selected' if status == 'completed' }}>{{ _('Completed') }}</option>
                            <option value="archived" {{ 'selected' if status == 'archived' }}>{{ _('Archived') }}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="date_from" class="form-label">{{ _('Date From') }}</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="date_to" class="form-label">{{ _('Date To') }}</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                    </div>
                    
                    <div class="col-md-3">
                        <label for="priority" class="form-label">{{ _('Priority') }}</label>
                        <select class="form-select" id="priority" name="priority">
                            <option value="">{{ _('All Priorities') }}</option>
                            <option value="low">{{ _('Low') }}</option>
                            <option value="medium">{{ _('Medium') }}</option>
                            <option value="high">{{ _('High') }}</option>
                            <option value="urgent">{{ _('Urgent') }}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>{{ _('Search') }}
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Search Results -->
{% if messages %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>{{ _('Search Results') }}
                </h5>
                <span class="badge bg-primary">{{ messages.total }} {{ _('results found') }}</span>
            </div>
            <div class="card-body">
                {% if messages.items %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>{{ _('Number') }}</th>
                                <th>{{ _('Type') }}</th>
                                <th>{{ _('Subject') }}</th>
                                <th>{{ _('Sender/Recipient') }}</th>
                                <th>{{ _('Status') }}</th>
                                <th>{{ _('Priority') }}</th>
                                <th>{{ _('Date') }}</th>
                                <th>{{ _('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for message in messages.items %}
                            <tr>
                                <td>
                                    <span class="badge bg-{{ 'primary' if message.message_type.value == 'incoming' else 'success' if message.message_type.value == 'outgoing' else 'info' }}">
                                        {{ message.message_number }}
                                    </span>
                                </td>
                                <td>
                                    <i class="fas fa-{{ 'inbox' if message.message_type.value == 'incoming' else 'paper-plane' if message.message_type.value == 'outgoing' else 'exchange-alt' }} me-1"></i>
                                    {{ _(message.message_type.value.title()) }}
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" class="text-decoration-none">
                                        {{ message.subject_ar if get_locale() == 'ar' and message.subject_ar else message.subject }}
                                    </a>
                                    {% if message.is_confidential %}
                                        <i class="fas fa-lock text-warning ms-1" title="{{ _('Confidential') }}"></i>
                                    {% endif %}
                                    {% if message.attachments.count() > 0 %}
                                        <i class="fas fa-paperclip text-info ms-1" title="{{ _('Has Attachments') }}"></i>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if message.message_type.value == 'incoming' %}
                                        {% if message.sender %}
                                            {{ message.sender.full_name_ar if get_locale() == 'ar' and message.sender.full_name_ar else message.sender.full_name }}
                                        {% else %}
                                            {{ message.external_sender }}
                                        {% endif %}
                                    {% else %}
                                        {% if message.recipient %}
                                            {{ message.recipient.full_name_ar if get_locale() == 'ar' and message.recipient.full_name_ar else message.recipient.full_name }}
                                        {% else %}
                                            {{ message.external_recipient }}
                                        {% endif %}
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'warning' if message.status.value == 'pending' else 'info' if message.status.value == 'in_progress' else 'success' if message.status.value == 'completed' else 'secondary' }}">
                                        {{ _(message.status.value.replace('_', ' ').title()) }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if message.priority.value == 'urgent' else 'warning' if message.priority.value == 'high' else 'info' if message.priority.value == 'medium' else 'secondary' }}">
                                        {{ _(message.priority.value.title()) }}
                                    </span>
                                </td>
                                <td>
                                    <small>{{ message.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    <a href="{{ url_for('messages.view_message', id=message.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="{{ _('View') }}">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                {% if messages.pages > 1 %}
                <nav aria-label="{{ _('Search results pagination') }}">
                    <ul class="pagination justify-content-center">
                        {% if messages.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('messages.search', page=messages.prev_num, **request.args) }}">
                                    {{ _('Previous') }}
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in messages.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != messages.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('messages.search', page=page_num, **request.args) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if messages.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('messages.search', page=messages.next_num, **request.args) }}">
                                    {{ _('Next') }}
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                
                {% else %}
                <div class="text-center text-muted py-5">
                    <i class="fas fa-search fa-4x mb-3 opacity-50"></i>
                    <h5>{{ _('No messages found') }}</h5>
                    <p>{{ _('Try adjusting your search criteria.') }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% elif query or message_type or status or date_from or date_to %}
<div class="row">
    <div class="col-12">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {{ _('No results found for your search criteria. Please try different keywords or filters.') }}
        </div>
    </div>
</div>
{% else %}
<div class="row">
    <div class="col-12">
        <div class="text-center text-muted py-5">
            <i class="fas fa-search fa-4x mb-3 opacity-50"></i>
            <h5>{{ _('Search Messages') }}</h5>
            <p>{{ _('Use the form above to search for messages by various criteria.') }}</p>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
